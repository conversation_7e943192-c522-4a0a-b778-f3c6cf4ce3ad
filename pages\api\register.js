import { validateActivationCode, useActivationCode } from '../../lib/activation-codes'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', 'POST')
    return res.status(405).json({ error: 'Method Not Allowed' })
  }

  const {
    activationCode,
    username,
    fullName,
    semester,
    program,
    personalEmail,
    password
  } = req.body

  // 验证必填字段
  if (!activationCode || !username || !fullName || !semester || !program || !personalEmail || !password) {
    return res.status(400).json({
      error: 'MISSING_FIELDS',
      message: '请填写所有必填字段'
    })
  }

  // 验证激活码
  const codeValidation = validateActivationCode(activationCode)
  if (!codeValidation.valid) {
    let message = '激活码无效'
    switch (codeValidation.error) {
      case 'INVALID_CODE':
        message = '激活码不存在'
        break
      case 'DISABLED_CODE':
        message = '激活码已被禁用'
        break
      case 'USED_CODE':
        message = '激活码已被使用'
        break
      case 'EXPIRED_CODE':
        message = '激活码已过期'
        break
    }
    return res.status(400).json({
      error: codeValidation.error,
      message
    })
  }

  // 构建学生邮箱
  const rawDom = process.env.EMAIL_DOMAIN || 'ghs.edu.kg'
  const domain = rawDom.startsWith('@') ? rawDom : '@' + rawDom
  const studentEmail = `${username}${domain}`

  try {
    // 获取 Google Access Token
    const tokenRes = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
        grant_type: 'refresh_token'
      })
    })

    if (!tokenRes.ok) {
      console.error('Token error:', await tokenRes.text())
      return res.status(500).json({
        error: 'TOKEN_ERROR',
        message: '服务器配置错误，请联系管理员'
      })
    }

    const { access_token } = await tokenRes.json()

    // 检查用户是否已存在
    const checkRes = await fetch(
      `https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(studentEmail)}`,
      { headers: { Authorization: `Bearer ${access_token}` } }
    )

    if (checkRes.ok) {
      return res.status(400).json({
        error: 'USER_EXISTS',
        message: '该用户名已被注册'
      })
    }

    // 拆分姓名
    const nameParts = fullName.trim().split(' ')
    const givenName = nameParts[0]
    const familyName = nameParts.slice(1).join(' ') || givenName

    // 创建 Google Workspace 用户
    const createRes = await fetch(
      'https://admin.googleapis.com/admin/directory/v1/users',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: { givenName, familyName },
          password,
          primaryEmail: studentEmail,
          recoveryEmail: personalEmail
        })
      }
    )

    if (!createRes.ok) {
      const errorText = await createRes.text()
      console.error('Create user error:', errorText)
      return res.status(500).json({
        error: 'CREATE_USER_ERROR',
        message: '创建用户账号失败，请稍后重试'
      })
    }

    // 标记激活码为已使用
    const codeUsed = useActivationCode(activationCode, studentEmail)
    if (!codeUsed) {
      console.error('Failed to mark activation code as used')
      // 这里不返回错误，因为用户已经创建成功
    }

    // 注册成功
    res.status(200).json({
      success: true,
      message: '注册成功！请使用您的学校邮箱登录',
      studentEmail
    })

  } catch (error) {
    console.error('Registration error:', error)
    res.status(500).json({
      error: 'SERVER_ERROR',
      message: '服务器错误，请稍后重试'
    })
  }
}