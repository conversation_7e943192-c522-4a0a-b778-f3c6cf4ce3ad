"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/register";
exports.ids = ["pages/api/register"];
exports.modules = {

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "(api)/./pages/api/register.js":
/*!*******************************!*\
  !*** ./pages/api/register.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        res.setHeader(\"Allow\", \"POST\");\n        return res.status(405).end(\"Method Not Allowed\");\n    }\n    const cookies = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(req.headers.cookie || \"\");\n    const oauthUsername = cookies.oauthUsername;\n    const trustLevel = parseInt(cookies.oauthTrustLevel || \"0\", 10);\n    if (!oauthUsername || trustLevel < 3) {\n        return res.status(403).end(\"Forbidden\");\n    }\n    const { fullName , semester , program , personalEmail , password  } = req.body;\n    if (!fullName || !semester || !program || !personalEmail || !password) {\n        return res.status(400).end(\"Missing fields\");\n    }\n    // 学生邮箱\n    const rawDom = process.env.EMAIL_DOMAIN || \"chatgpt.org.uk\";\n    const domain = rawDom.startsWith(\"@\") ? rawDom : \"@\" + rawDom;\n    const studentEmail = oauthUsername.includes(\"@\") ? oauthUsername : `${oauthUsername}${domain}`;\n    // 防重复注册：询问 Google 目录\n    const tokenRes = await fetch(\"https://oauth2.googleapis.com/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n            client_id: process.env.GOOGLE_CLIENT_ID,\n            client_secret: process.env.GOOGLE_CLIENT_SECRET,\n            refresh_token: process.env.GOOGLE_REFRESH_TOKEN,\n            grant_type: \"refresh_token\"\n        })\n    });\n    const { access_token  } = await tokenRes.json();\n    const dirRes = await fetch(`https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(studentEmail)}`, {\n        headers: {\n            Authorization: `Bearer ${access_token}`\n        }\n    });\n    if (dirRes.ok) {\n        return res.status(400).end(\"This student is already registered\");\n    }\n    // 拆分名字\n    let [givenName, ...rest] = fullName.trim().split(\" \");\n    const familyName = rest.join(\" \") || givenName;\n    // 创建 G Suite 用户\n    const createRes = await fetch(\"https://admin.googleapis.com/admin/directory/v1/users\", {\n        method: \"POST\",\n        headers: {\n            Authorization: `Bearer ${access_token}`,\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            name: {\n                givenName,\n                familyName\n            },\n            password,\n            primaryEmail: studentEmail,\n            recoveryEmail: personalEmail\n        })\n    });\n    if (!createRes.ok) {\n        console.error(\"Create user error:\", await createRes.text());\n        return res.status(500).end(\"Could not create student account\");\n    }\n    // 完成后直奔 portal\n    res.redirect(302, \"/student-portal\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/register.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/register.js"));
module.exports = __webpack_exports__;

})();