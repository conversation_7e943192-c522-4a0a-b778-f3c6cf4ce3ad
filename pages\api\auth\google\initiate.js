// pages/api/auth/google/initiate.js
import { v4 as uuidv4 } from 'uuid'

export default function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  // 生成状态参数用于防止CSRF攻击
  const state = uuidv4()
  
  // 设置状态cookie
  res.setHeader('Set-<PERSON>ie', `googleOAuthState=${state}; Path=/; HttpOnly; Secure; SameSite=Lax; Max-Age=600`)
  
  // 构建Google OAuth授权URL
  const clientId = process.env.GOOGLE_OAUTH_CLIENT_ID
  const redirectUri = process.env.GOOGLE_OAUTH_REDIRECT_URI || `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/auth/google/callback`
  
  const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth')
  authUrl.searchParams.set('client_id', clientId)
  authUrl.searchParams.set('redirect_uri', redirectUri)
  authUrl.searchParams.set('response_type', 'code')
  authUrl.searchParams.set('scope', 'openid email profile')
  authUrl.searchParams.set('state', state)
  authUrl.searchParams.set('access_type', 'offline')
  authUrl.searchParams.set('prompt', 'consent')
  
  // 重定向到Google授权页面
  res.redirect(authUrl.toString())
}
