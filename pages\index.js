import Head from 'next/head'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

export default function Home() {
  const router = useRouter()
  const [showWelcome, setShowWelcome] = useState(false)

  useEffect(() => {
    // 添加一个小延迟来显示欢迎动画
    const timer = setTimeout(() => setShowWelcome(true), 100)
    return () => clearTimeout(timer)
  }, [])

  return (
    <>
      <Head>
        <title>Great Heights School - 学生注册系统</title>
        <meta name="description" content="Great Heights School 学生注册和登录系统" />
      </Head>
      <div className="container">
        <div className={`welcome-card ${showWelcome ? 'show' : ''}`}>
          <div className="logo-section">
            <div className="logo-placeholder">
              {/* 这里将来可以放置学校Logo */}
              <div className="logo-icon">🎓</div>
            </div>
            <h1>Great Heights School</h1>
            <p className="subtitle">学生注册与登录系统</p>
          </div>

          <div className="action-section">
            <div className="action-card">
              <h3>新学生注册</h3>
              <p>使用激活码创建您的学校账号</p>
              <a href="/register" className="btn btn-primary">
                立即注册
              </a>
            </div>

            <div className="divider">
              <span>或</span>
            </div>

            <div className="action-card">
              <h3>学生登录</h3>
              <p>使用您的学校邮箱登录系统</p>
              <a href="/login" className="btn btn-secondary">
                登录账号
              </a>
            </div>
          </div>

          <div className="info-section">
            <div className="info-item">
              <span className="icon">📧</span>
              <span>仅限 @ghs.edu.kg 邮箱</span>
            </div>
            <div className="info-item">
              <span className="icon">🔐</span>
              <span>安全的Google OAuth认证</span>
            </div>
            <div className="info-item">
              <span className="icon">🎯</span>
              <span>需要有效激活码注册</span>
            </div>
          </div>
        </div>

        <footer>
          <p>
            Powered by{' '}
            <a href="#" target="_blank" rel="noopener">
              Garbage Human Studio
            </a>
          </p>
          <p className="copyright">
            © 2025 Great Heights School. All rights reserved.
          </p>
        </footer>
      </div>
      <style jsx>{`
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }
        .welcome-card {
          background: #fff;
          padding: 50px 40px;
          border-radius: 16px;
          box-shadow: 0 20px 60px rgba(0,0,0,0.15);
          text-align: center;
          max-width: 500px;
          width: 100%;
          opacity: 0;
          transform: translateY(30px);
          transition: all 0.6s ease;
        }
        .welcome-card.show {
          opacity: 1;
          transform: translateY(0);
        }
        .logo-section {
          margin-bottom: 40px;
        }
        .logo-placeholder {
          margin-bottom: 20px;
        }
        .logo-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }
        .logo-section h1 {
          color: #333;
          margin-bottom: 8px;
          font-size: 32px;
          font-weight: 700;
          letter-spacing: -0.5px;
        }
        .subtitle {
          color: #666;
          font-size: 16px;
          margin: 0;
        }
        .action-section {
          margin-bottom: 40px;
        }
        .action-card {
          background: #f8f9fa;
          padding: 24px;
          border-radius: 12px;
          margin-bottom: 16px;
          border: 2px solid transparent;
          transition: all 0.3s ease;
        }
        .action-card:hover {
          border-color: #e9ecef;
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .action-card h3 {
          color: #333;
          margin-bottom: 8px;
          font-size: 20px;
          font-weight: 600;
        }
        .action-card p {
          color: #666;
          margin-bottom: 16px;
          font-size: 14px;
        }
        .divider {
          margin: 24px 0;
          position: relative;
          color: #999;
          font-size: 14px;
        }
        .divider::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          background: #e9ecef;
          z-index: 1;
        }
        .divider span {
          background: #fff;
          padding: 0 16px;
          position: relative;
          z-index: 2;
        }
        .btn {
          display: inline-block;
          padding: 12px 32px;
          border-radius: 8px;
          text-decoration: none;
          font-size: 16px;
          font-weight: 500;
          transition: all 0.3s ease;
          border: 2px solid transparent;
        }
        .btn-primary {
          background: #4285f4;
          color: #fff;
        }
        .btn-primary:hover {
          background: #3367d6;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
        }
        .btn-secondary {
          background: #fff;
          color: #4285f4;
          border-color: #4285f4;
        }
        .btn-secondary:hover {
          background: #4285f4;
          color: #fff;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
        }
        .info-section {
          border-top: 1px solid #e9ecef;
          padding-top: 24px;
        }
        .info-item {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin-bottom: 12px;
          color: #666;
          font-size: 14px;
        }
        .info-item:last-child {
          margin-bottom: 0;
        }
        .icon {
          font-size: 16px;
        }
        footer {
          margin-top: 40px;
          text-align: center;
          color: rgba(255,255,255,0.8);
          font-size: 14px;
        }
        footer p {
          margin: 4px 0;
        }
        footer a {
          color: rgba(255,255,255,0.9);
          text-decoration: none;
          font-weight: 500;
        }
        footer a:hover {
          text-decoration: underline;
        }
        .copyright {
          font-size: 12px;
          opacity: 0.7;
        }
        @media (max-width: 480px) {
          .welcome-card {
            padding: 30px 20px;
          }
          .logo-section h1 {
            font-size: 28px;
          }
          .action-card {
            padding: 20px;
          }
        }
      `}</style>
    </>
  )
}
