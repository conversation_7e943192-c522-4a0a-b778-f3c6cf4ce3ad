"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/oauth2/callback";
exports.ids = ["pages/api/oauth2/callback"];
exports.modules = {

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "(api)/./pages/api/oauth2/callback.js":
/*!**************************************!*\
  !*** ./pages/api/oauth2/callback.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    const { code , state  } = req.query;\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_0___default().parse(req.headers.cookie || \"\");\n    if (cookies.oauthState !== state) {\n        return res.status(400).send(\"Invalid state\");\n    }\n    // 获取 OAuth2 Token\n    const tokenRes = await fetch(process.env.TOKEN_ENDPOINT, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n            grant_type: \"authorization_code\",\n            code,\n            redirect_uri: process.env.OAUTH_REDIRECT_URI,\n            client_id: process.env.CLIENT_ID,\n            client_secret: process.env.CLIENT_SECRET\n        })\n    });\n    if (!tokenRes.ok) return res.status(500).send(\"Token error\");\n    const { access_token  } = await tokenRes.json();\n    // 获取用户信息\n    const userRes = await fetch(process.env.USER_ENDPOINT, {\n        headers: {\n            Authorization: `Bearer ${access_token}`\n        }\n    });\n    if (!userRes.ok) return res.status(500).send(\"User info error\");\n    const { id , username , trust_level  } = await userRes.json();\n    // 写入 OAuth Cookie\n    res.setHeader(\"Set-Cookie\", [\n        cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"oauthUsername\", username, {\n            path: \"/\",\n            httpOnly: true,\n            secure: true,\n            sameSite: \"lax\"\n        }),\n        cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"oauthUserId\", String(id), {\n            path: \"/\",\n            httpOnly: true,\n            secure: true,\n            sameSite: \"lax\"\n        }),\n        cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"oauthTrustLevel\", String(trust_level), {\n            path: \"/\",\n            httpOnly: true,\n            secure: true,\n            sameSite: \"lax\"\n        })\n    ]);\n    res.redirect(\"/register\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/oauth2/callback.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/oauth2/callback.js"));
module.exports = __webpack_exports__;

})();