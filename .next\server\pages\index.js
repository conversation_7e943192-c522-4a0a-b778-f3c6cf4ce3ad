"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-7255e037a1306e26\",\n                    children: \"ChatGPT University\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                    lineNumber: 6,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-7255e037a1306e26\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-7255e037a1306e26\" + \" \" + \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"jsx-7255e037a1306e26\",\n                                children: \"Welcome to ChatGPT University\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                                lineNumber: 9,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-7255e037a1306e26\",\n                                children: \"Please authenticate via Linux.do to continue:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                                lineNumber: 10,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/api/oauth2/initiate\",\n                                className: \"jsx-7255e037a1306e26\" + \" \" + \"button\",\n                                children: \"Sign in with Linux.do\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                                lineNumber: 11,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7255e037a1306e26\" + \" \" + \"cta\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-7255e037a1306e26\",\n                                        children: \"If this platform helps you, please give me a \\uD83D\\uDC4D and follow me!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                                        lineNumber: 15,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-7255e037a1306e26\",\n                                        children: \"Your support keeps me improving.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                                        lineNumber: 16,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-7255e037a1306e26\",\n                        children: [\n                            \"Powered by\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://www.chatgpt.org.uk/\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"jsx-7255e037a1306e26\",\n                                children: \"chatgpt.org.uk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\index.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"7255e037a1306e26\",\n                children: \".container.jsx-7255e037a1306e26{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:-webkit-linear-gradient(315deg,#f0f4f8,#d9e2ec);background:-moz-linear-gradient(315deg,#f0f4f8,#d9e2ec);background:-o-linear-gradient(315deg,#f0f4f8,#d9e2ec);background:linear-gradient(135deg,#f0f4f8,#d9e2ec);padding:20px}.card.jsx-7255e037a1306e26{background:#fff;padding:40px;-webkit-border-radius:10px;-moz-border-radius:10px;border-radius:10px;-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);-moz-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1);text-align:center;max-width:480px;width:100%}h1.jsx-7255e037a1306e26{color:#333;margin-bottom:20px}p.jsx-7255e037a1306e26{color:#555;margin:10px 0}.button.jsx-7255e037a1306e26{display:inline-block;margin-top:20px;padding:12px 24px;background:#0070f3;color:#fff;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;text-decoration:none;font-size:18px;-webkit-transition:background.3s;-moz-transition:background.3s;-o-transition:background.3s;transition:background.3s}.button.jsx-7255e037a1306e26:hover{background:#005bb5}.cta.jsx-7255e037a1306e26{margin-top:30px;color:#444;font-size:16px}footer.jsx-7255e037a1306e26{margin-top:40px;color:#777;font-size:14px}footer.jsx-7255e037a1306e26 a.jsx-7255e037a1306e26{color:#0070f3;text-decoration:none}footer.jsx-7255e037a1306e26 a.jsx-7255e037a1306e26:hover{text-decoration:underline}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.js\n");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/index.js"));
module.exports = __webpack_exports__;

})();