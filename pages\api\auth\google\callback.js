// pages/api/auth/google/callback.js
import cookie from 'cookie'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  const { code, state, error } = req.query
  
  // 检查是否有错误
  if (error) {
    console.error('Google OAuth error:', error)
    return res.redirect('/?error=oauth_error')
  }

  // 验证state参数
  const cookies = cookie.parse(req.headers.cookie || '')
  if (cookies.googleOAuthState !== state) {
    console.error('Invalid state parameter')
    return res.redirect('/?error=invalid_state')
  }

  if (!code) {
    console.error('No authorization code received')
    return res.redirect('/?error=no_code')
  }

  try {
    // 交换授权码获取访问令牌
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_OAUTH_CLIENT_ID,
        client_secret: process.env.GOOGLE_OAUTH_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: process.env.GOOGLE_OAUTH_REDIRECT_URI || `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/auth/google/callback`,
      }),
    })

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text()
      console.error('Token exchange error:', errorText)
      return res.redirect('/?error=token_error')
    }

    const tokenData = await tokenResponse.json()
    const { access_token, id_token } = tokenData

    // 获取用户信息
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    })

    if (!userResponse.ok) {
      console.error('Failed to fetch user info')
      return res.redirect('/?error=user_info_error')
    }

    const userData = await userResponse.json()
    const { email, name, picture } = userData

    // 验证邮箱后缀
    const allowedDomain = process.env.EMAIL_DOMAIN || 'ghs.edu.kg'
    const domain = allowedDomain.startsWith('@') ? allowedDomain.slice(1) : allowedDomain
    
    if (!email.endsWith(`@${domain}`)) {
      console.log(`Login attempt with invalid email domain: ${email}`)
      return res.redirect(`/?error=invalid_domain&domain=${domain}`)
    }

    // 检查用户是否在Google Workspace中存在
    const adminTokenRes = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
        grant_type: 'refresh_token'
      })
    })

    if (adminTokenRes.ok) {
      const { access_token: adminToken } = await adminTokenRes.json()
      
      // 检查用户是否在Google Directory中存在
      const directoryRes = await fetch(
        `https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(email)}`,
        { headers: { Authorization: `Bearer ${adminToken}` } }
      )

      if (!directoryRes.ok) {
        console.log(`User not found in Google Directory: ${email}`)
        return res.redirect('/?error=user_not_found')
      }
    }

    // 设置登录session cookies
    const sessionCookies = [
      cookie.serialize('userEmail', email, { 
        path: '/', 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production', 
        sameSite: 'lax',
        maxAge: 24 * 60 * 60 // 24小时
      }),
      cookie.serialize('userName', name, { 
        path: '/', 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production', 
        sameSite: 'lax',
        maxAge: 24 * 60 * 60
      }),
      cookie.serialize('userPicture', picture || '', { 
        path: '/', 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production', 
        sameSite: 'lax',
        maxAge: 24 * 60 * 60
      }),
      // 清除OAuth状态cookie
      cookie.serialize('googleOAuthState', '', { 
        path: '/', 
        expires: new Date(0) 
      })
    ]

    res.setHeader('Set-Cookie', sessionCookies)

    // 登录成功，重定向到学生门户
    res.redirect('/student-portal')

  } catch (error) {
    console.error('OAuth callback error:', error)
    res.redirect('/?error=server_error')
  }
}
