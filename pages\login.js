import Head from 'next/head'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

export default function Login() {
  const router = useRouter()
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // 处理URL中的错误参数
    if (router.query.error) {
      switch (router.query.error) {
        case 'oauth_error':
          setError('Google登录过程中出现错误，请重试')
          break
        case 'invalid_state':
          setError('登录验证失败，请重试')
          break
        case 'no_code':
          setError('未收到授权码，请重试')
          break
        case 'token_error':
          setError('获取访问令牌失败，请重试')
          break
        case 'user_info_error':
          setError('获取用户信息失败，请重试')
          break
        case 'invalid_domain':
          const domain = router.query.domain || 'ghs.edu.kg'
          setError(`只允许 @${domain} 邮箱登录`)
          break
        case 'user_not_found':
          setError('用户不存在，请先注册或联系管理员')
          break
        case 'server_error':
          setError('服务器错误，请稍后重试')
          break
        default:
          setError('登录失败，请重试')
      }
    }
  }, [router.query])

  const handleGoogleLogin = () => {
    setIsLoading(true)
    setError('')
    window.location.href = '/api/auth/google/initiate'
  }

  return (
    <>
      <Head>
        <title>登录 - Great Heights School</title>
      </Head>
      <div className="container">
        <div className="card">
          <div className="logo-section">
            <h1>Great Heights School</h1>
            <p>学生登录系统</p>
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <div className="login-section">
            <p className="login-instruction">
              请使用您的学校邮箱 (@ghs.edu.kg) 登录
            </p>
            
            <button 
              onClick={handleGoogleLogin}
              disabled={isLoading}
              className="google-login-btn"
            >
              {isLoading ? (
                <span className="loading">
                  <span className="spinner"></span>
                  登录中...
                </span>
              ) : (
                <span className="google-login-content">
                  <svg className="google-icon" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  使用 Google 登录
                </span>
              )}
            </button>
          </div>

          <div className="register-link">
            还没有账号？<a href="/register">点击注册</a>
          </div>
        </div>

        <footer>
          Powered by{' '}
          <a href="#" target="_blank" rel="noopener">
            Garbage Human Studio
          </a>
        </footer>
      </div>

      <style jsx>{`
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }
        .card {
          background: #fff;
          max-width: 400px;
          width: 100%;
          padding: 40px;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.1);
          text-align: center;
        }
        .logo-section h1 {
          color: #333;
          margin-bottom: 8px;
          font-size: 28px;
          font-weight: 600;
        }
        .logo-section p {
          color: #666;
          margin-bottom: 30px;
          font-size: 16px;
        }
        .error-message {
          background: #fee;
          color: #c33;
          padding: 12px;
          border-radius: 6px;
          margin-bottom: 20px;
          border: 1px solid #fcc;
          font-size: 14px;
        }
        .login-instruction {
          color: #555;
          margin-bottom: 24px;
          font-size: 14px;
          line-height: 1.5;
        }
        .google-login-btn {
          width: 100%;
          padding: 14px 20px;
          background: #fff;
          border: 2px solid #ddd;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;
          font-weight: 500;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
        }
        .google-login-btn:hover:not(:disabled) {
          border-color: #4285f4;
          box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
        }
        .google-login-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
        .google-login-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        .google-icon {
          width: 20px;
          height: 20px;
        }
        .loading {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .spinner {
          width: 16px;
          height: 16px;
          border: 2px solid #f3f3f3;
          border-top: 2px solid #4285f4;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .register-link {
          margin-top: 24px;
          color: #666;
          font-size: 14px;
        }
        .register-link a {
          color: #4285f4;
          text-decoration: none;
          font-weight: 500;
        }
        .register-link a:hover {
          text-decoration: underline;
        }
        footer {
          margin-top: 30px;
          color: rgba(255,255,255,0.8);
          font-size: 14px;
        }
        footer a {
          color: rgba(255,255,255,0.9);
          text-decoration: none;
        }
        footer a:hover {
          text-decoration: underline;
        }
      `}</style>
    </>
  )
}
