"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/oauth2/initiate";
exports.ids = ["pages/api/oauth2/initiate"];
exports.modules = {

/***/ "uuid":
/*!***********************!*\
  !*** external "uuid" ***!
  \***********************/
/***/ ((module) => {

module.exports = import("uuid");;

/***/ }),

/***/ "(api)/./pages/api/oauth2/initiate.js":
/*!**************************************!*\
  !*** ./pages/api/oauth2/initiate.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"uuid\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([uuid__WEBPACK_IMPORTED_MODULE_0__]);\nuuid__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nfunction handler(req, res) {\n    const state = (0,uuid__WEBPACK_IMPORTED_MODULE_0__.v4)();\n    res.setHeader(\"Set-Cookie\", `oauthState=${state}; Path=/; HttpOnly; Secure; SameSite=Lax`);\n    const redirectUri = encodeURIComponent(process.env.OAUTH_REDIRECT_URI);\n    const authUrl = `${process.env.AUTHORIZATION_ENDPOINT}` + `?client_id=${process.env.CLIENT_ID}` + `&response_type=code` + `&redirect_uri=${redirectUri}` + `&state=${state}`;\n    res.redirect(authUrl);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9wYWdlcy9hcGkvb2F1dGgyL2luaXRpYXRlLmpzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW1DO0FBRXBCLFNBQVNFLFFBQVFDLEdBQUcsRUFBRUMsR0FBRyxFQUFFO0lBQ3hDLE1BQU1DLFFBQVFKLHdDQUFNQTtJQUNwQkcsSUFBSUUsU0FBUyxDQUFDLGNBQWMsQ0FBQyxXQUFXLEVBQUVELE1BQU0sd0NBQXdDLENBQUM7SUFDekYsTUFBTUUsY0FBY0MsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLGtCQUFrQjtJQUNyRSxNQUFNQyxVQUNKLENBQUMsRUFBRUgsUUFBUUMsR0FBRyxDQUFDRyxzQkFBc0IsQ0FBQyxDQUFDLEdBQ3ZDLENBQUMsV0FBVyxFQUFFSixRQUFRQyxHQUFHLENBQUNJLFNBQVMsQ0FBQyxDQUFDLEdBQ3JDLENBQUMsbUJBQW1CLENBQUMsR0FDckIsQ0FBQyxjQUFjLEVBQUVQLFlBQVksQ0FBQyxHQUM5QixDQUFDLE9BQU8sRUFBRUYsTUFBTSxDQUFDO0lBQ25CRCxJQUFJVyxRQUFRLENBQUNIO0FBQ2YsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL215LW5leHQtYXBwLy4vcGFnZXMvYXBpL29hdXRoMi9pbml0aWF0ZS5qcz84ZTBiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGhhbmRsZXIocmVxLCByZXMpIHtcbiAgY29uc3Qgc3RhdGUgPSB1dWlkdjQoKVxuICByZXMuc2V0SGVhZGVyKCdTZXQtQ29va2llJywgYG9hdXRoU3RhdGU9JHtzdGF0ZX07IFBhdGg9LzsgSHR0cE9ubHk7IFNlY3VyZTsgU2FtZVNpdGU9TGF4YClcbiAgY29uc3QgcmVkaXJlY3RVcmkgPSBlbmNvZGVVUklDb21wb25lbnQocHJvY2Vzcy5lbnYuT0FVVEhfUkVESVJFQ1RfVVJJKVxuICBjb25zdCBhdXRoVXJsID1cbiAgICBgJHtwcm9jZXNzLmVudi5BVVRIT1JJWkFUSU9OX0VORFBPSU5UfWAgK1xuICAgIGA/Y2xpZW50X2lkPSR7cHJvY2Vzcy5lbnYuQ0xJRU5UX0lEfWAgK1xuICAgIGAmcmVzcG9uc2VfdHlwZT1jb2RlYCArXG4gICAgYCZyZWRpcmVjdF91cmk9JHtyZWRpcmVjdFVyaX1gICtcbiAgICBgJnN0YXRlPSR7c3RhdGV9YFxuICByZXMucmVkaXJlY3QoYXV0aFVybClcbn1cbiJdLCJuYW1lcyI6WyJ2NCIsInV1aWR2NCIsImhhbmRsZXIiLCJyZXEiLCJyZXMiLCJzdGF0ZSIsInNldEhlYWRlciIsInJlZGlyZWN0VXJpIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwicHJvY2VzcyIsImVudiIsIk9BVVRIX1JFRElSRUNUX1VSSSIsImF1dGhVcmwiLCJBVVRIT1JJWkFUSU9OX0VORFBPSU5UIiwiQ0xJRU5UX0lEIiwicmVkaXJlY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./pages/api/oauth2/initiate.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/oauth2/initiate.js"));
module.exports = __webpack_exports__;

})();