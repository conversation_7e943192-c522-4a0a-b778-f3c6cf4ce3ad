// pages/api/validate-activation-code.js
import { validateActivationCode } from '../../lib/activation-codes'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  const { code } = req.body

  if (!code) {
    return res.status(400).json({ 
      valid: false, 
      error: 'MISSING_CODE',
      message: '请输入激活码' 
    })
  }

  try {
    const validation = validateActivationCode(code)
    
    if (validation.valid) {
      res.status(200).json({ 
        valid: true, 
        message: '激活码有效' 
      })
    } else {
      let message = '激活码无效'
      
      switch (validation.error) {
        case 'INVALID_CODE':
          message = '激活码不存在'
          break
        case 'DISABLED_CODE':
          message = '激活码已被禁用'
          break
        case 'USED_CODE':
          message = '激活码已被使用'
          break
        case 'EXPIRED_CODE':
          message = '激活码已过期'
          break
      }
      
      res.status(400).json({ 
        valid: false, 
        error: validation.error,
        message 
      })
    }
  } catch (error) {
    console.error('Error validating activation code:', error)
    res.status(500).json({ 
      valid: false, 
      error: 'SERVER_ERROR',
      message: '服务器错误，请稍后重试' 
    })
  }
}
