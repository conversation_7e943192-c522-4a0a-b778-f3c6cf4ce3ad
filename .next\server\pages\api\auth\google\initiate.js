"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/google/initiate";
exports.ids = ["pages/api/auth/google/initiate"];
exports.modules = {

/***/ "uuid":
/*!***********************!*\
  !*** external "uuid" ***!
  \***********************/
/***/ ((module) => {

module.exports = import("uuid");;

/***/ }),

/***/ "(api)/./pages/api/auth/google/initiate.js":
/*!*******************************************!*\
  !*** ./pages/api/auth/google/initiate.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"uuid\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([uuid__WEBPACK_IMPORTED_MODULE_0__]);\nuuid__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// pages/api/auth/google/initiate.js\n\nfunction handler(req, res) {\n    if (req.method !== \"GET\") {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    // 生成状态参数用于防止CSRF攻击\n    const state = (0,uuid__WEBPACK_IMPORTED_MODULE_0__.v4)();\n    // 设置状态cookie\n    res.setHeader(\"Set-Cookie\", `googleOAuthState=${state}; Path=/; HttpOnly; Secure; SameSite=Lax; Max-Age=600`);\n    // 构建Google OAuth授权URL\n    const clientId = process.env.GOOGLE_OAUTH_CLIENT_ID;\n    const redirectUri = process.env.GOOGLE_OAUTH_REDIRECT_URI || `${process.env.NEXTAUTH_URL || \"http://localhost:3000\"}/api/auth/google/callback`;\n    const authUrl = new URL(\"https://accounts.google.com/o/oauth2/v2/auth\");\n    authUrl.searchParams.set(\"client_id\", clientId);\n    authUrl.searchParams.set(\"redirect_uri\", redirectUri);\n    authUrl.searchParams.set(\"response_type\", \"code\");\n    authUrl.searchParams.set(\"scope\", \"openid email profile\");\n    authUrl.searchParams.set(\"state\", state);\n    authUrl.searchParams.set(\"access_type\", \"offline\");\n    authUrl.searchParams.set(\"prompt\", \"consent\");\n    // 重定向到Google授权页面\n    res.redirect(authUrl.toString());\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/auth/google/initiate.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/auth/google/initiate.js"));
module.exports = __webpack_exports__;

})();