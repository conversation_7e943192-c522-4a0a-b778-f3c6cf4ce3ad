"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/register";
exports.ids = ["pages/register"];
exports.modules = {

/***/ "./pages/register.js":
/*!***************************!*\
  !*** ./pages/register.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Register),\n/* harmony export */   \"getServerSideProps\": () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// 新的注册页面不需要服务端预处理，直接渲染\nasync function getServerSideProps() {\n    return {\n        props: {}\n    };\n}\nfunction Register() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        activationCode: \"\",\n        username: \"\",\n        fullName: \"\",\n        semester: \"Fall 2025\",\n        program: \"Master of Computer Science\",\n        password: \"\",\n        personalEmail: \"\"\n    });\n    const [isValidating, setIsValidating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [codeValidation, setCodeValidation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const handleInputChange = (e)=>{\n        const { name , value  } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // 清除激活码验证状态当用户修改激活码时\n        if (name === \"activationCode\") {\n            setCodeValidation(null);\n        }\n    };\n    const validateActivationCode = async ()=>{\n        if (!formData.activationCode.trim()) {\n            setCodeValidation({\n                valid: false,\n                message: \"请输入激活码\"\n            });\n            return;\n        }\n        setIsValidating(true);\n        try {\n            const response = await fetch(\"/api/validate-activation-code\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code: formData.activationCode\n                })\n            });\n            const result = await response.json();\n            setCodeValidation(result);\n        } catch (error) {\n            setCodeValidation({\n                valid: false,\n                message: \"验证失败，请稍后重试\"\n            });\n        } finally{\n            setIsValidating(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // 首先验证激活码\n        if (!codeValidation || !codeValidation.valid) {\n            setSubmitMessage(\"请先验证激活码\");\n            return;\n        }\n        setIsSubmitting(true);\n        setSubmitMessage(\"\");\n        try {\n            const response = await fetch(\"/api/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (response.ok) {\n                setSubmitMessage(\"注册成功！正在跳转到登录页面...\");\n                setTimeout(()=>{\n                    window.location.href = \"/login\";\n                }, 2000);\n            } else {\n                setSubmitMessage(result.message || \"注册失败，请稍后重试\");\n            }\n        } catch (error) {\n            setSubmitMessage(\"注册失败，请稍后重试\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-7e5101c32fcbacf1\",\n                    children: \"学生注册 - Great Heights School\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                    lineNumber: 95,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-7e5101c32fcbacf1\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-7e5101c32fcbacf1\" + \" \" + \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"jsx-7e5101c32fcbacf1\",\n                                children: \"学生注册\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"jsx-7e5101c32fcbacf1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7e5101c32fcbacf1\" + \" \" + \"activation-code-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"activationCode\",\n                                                className: \"jsx-7e5101c32fcbacf1\",\n                                                children: \"激活码 *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7e5101c32fcbacf1\" + \" \" + \"code-input-group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"activationCode\",\n                                                        name: \"activationCode\",\n                                                        value: formData.activationCode,\n                                                        onChange: handleInputChange,\n                                                        placeholder: \"请输入激活码\",\n                                                        required: true,\n                                                        className: \"jsx-7e5101c32fcbacf1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: validateActivationCode,\n                                                        disabled: isValidating || !formData.activationCode.trim(),\n                                                        className: \"jsx-7e5101c32fcbacf1\" + \" \" + \"validate-btn\",\n                                                        children: isValidating ? \"验证中...\" : \"验证\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this),\n                                            codeValidation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-7e5101c32fcbacf1\" + \" \" + `validation-message ${codeValidation.valid ? \"valid\" : \"invalid\"}`,\n                                                children: codeValidation.message\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"username\",\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: \"用户名 *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"username\",\n                                        name: \"username\",\n                                        value: formData.username,\n                                        onChange: handleInputChange,\n                                        placeholder: \"请输入用户名\",\n                                        required: true,\n                                        className: \"jsx-7e5101c32fcbacf1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"fullName\",\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: \"姓名 *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"fullName\",\n                                        name: \"fullName\",\n                                        value: formData.fullName,\n                                        onChange: handleInputChange,\n                                        placeholder: \"请输入您的姓名\",\n                                        required: true,\n                                        className: \"jsx-7e5101c32fcbacf1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"semester\",\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: \"学期 *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"semester\",\n                                        name: \"semester\",\n                                        value: formData.semester,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Fall 2025\",\n                                                className: \"jsx-7e5101c32fcbacf1\",\n                                                children: \"Fall 2025\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Spring 2025\",\n                                                className: \"jsx-7e5101c32fcbacf1\",\n                                                children: \"Spring 2025\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Summer 2025\",\n                                                className: \"jsx-7e5101c32fcbacf1\",\n                                                children: \"Summer 2025\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"program\",\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: \"专业 *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"program\",\n                                        name: \"program\",\n                                        value: formData.program,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Master of Computer Science\",\n                                                className: \"jsx-7e5101c32fcbacf1\",\n                                                children: \"计算机科学硕士\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Master of Business Administration\",\n                                                className: \"jsx-7e5101c32fcbacf1\",\n                                                children: \"工商管理硕士\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Master of Education\",\n                                                className: \"jsx-7e5101c32fcbacf1\",\n                                                children: \"教育学硕士\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: \"密码 *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        id: \"password\",\n                                        name: \"password\",\n                                        value: formData.password,\n                                        onChange: handleInputChange,\n                                        placeholder: \"请设置密码\",\n                                        required: true,\n                                        className: \"jsx-7e5101c32fcbacf1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"personalEmail\",\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: \"个人邮箱 *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"personalEmail\",\n                                        name: \"personalEmail\",\n                                        value: formData.personalEmail,\n                                        onChange: handleInputChange,\n                                        placeholder: \"请输入您的个人邮箱\",\n                                        required: true,\n                                        className: \"jsx-7e5101c32fcbacf1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    submitMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-7e5101c32fcbacf1\" + \" \" + `submit-message ${submitMessage.includes(\"成功\") ? \"success\" : \"error\"}`,\n                                        children: submitMessage\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || !codeValidation?.valid,\n                                        className: \"jsx-7e5101c32fcbacf1\" + \" \" + \"submit-btn\",\n                                        children: isSubmitting ? \"注册中...\" : \"注册\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7e5101c32fcbacf1\" + \" \" + \"login-link\",\n                                children: [\n                                    \"已有账号？\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/login\",\n                                        className: \"jsx-7e5101c32fcbacf1\",\n                                        children: \"点击登录\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 214,\n                                        columnNumber: 18\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-7e5101c32fcbacf1\",\n                        children: [\n                            \"Powered by\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                target: \"_blank\",\n                                rel: \"noopener\",\n                                className: \"jsx-7e5101c32fcbacf1\",\n                                children: \"Garbage Human Studio\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"7e5101c32fcbacf1\",\n                children: \".container.jsx-7e5101c32fcbacf1{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#f0f4f8;padding:20px}.card.jsx-7e5101c32fcbacf1{background:#fff;max-width:500px;width:100%;padding:40px;-webkit-border-radius:10px;-moz-border-radius:10px;border-radius:10px;-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);-moz-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1)}h1.jsx-7e5101c32fcbacf1{text-align:center;color:#333;margin-bottom:30px;font-size:28px}label.jsx-7e5101c32fcbacf1{display:block;margin:15px 0 5px;color:#555;font-weight:500}input.jsx-7e5101c32fcbacf1,select.jsx-7e5101c32fcbacf1{width:100%;padding:12px;border:1px solid#ddd;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:16px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}input.jsx-7e5101c32fcbacf1:focus,select.jsx-7e5101c32fcbacf1:focus{outline:none;border-color:#0070f3;-webkit-box-shadow:0 0 0 2px rgba(0,112,243,.1);-moz-box-shadow:0 0 0 2px rgba(0,112,243,.1);box-shadow:0 0 0 2px rgba(0,112,243,.1)}.activation-code-section.jsx-7e5101c32fcbacf1{margin-bottom:20px}.code-input-group.jsx-7e5101c32fcbacf1{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:10px}.code-input-group.jsx-7e5101c32fcbacf1 input.jsx-7e5101c32fcbacf1{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1}.validate-btn.jsx-7e5101c32fcbacf1{padding:12px 20px;background:#28a745;color:white;border:none;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;font-size:14px;white-space:nowrap}.validate-btn.jsx-7e5101c32fcbacf1:hover:not(:disabled){background:#218838}.validate-btn.jsx-7e5101c32fcbacf1:disabled{background:#6c757d;cursor:not-allowed}.validation-message.jsx-7e5101c32fcbacf1{margin-top:8px;padding:8px 12px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:14px}.validation-message.valid.jsx-7e5101c32fcbacf1{background:#d4edda;color:#155724;border:1px solid#c3e6cb}.validation-message.invalid.jsx-7e5101c32fcbacf1{background:#f8d7da;color:#721c24;border:1px solid#f5c6cb}.submit-message.jsx-7e5101c32fcbacf1{margin:15px 0;padding:12px;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;text-align:center;font-weight:500}.submit-message.success.jsx-7e5101c32fcbacf1{background:#d4edda;color:#155724;border:1px solid#c3e6cb}.submit-message.error.jsx-7e5101c32fcbacf1{background:#f8d7da;color:#721c24;border:1px solid#f5c6cb}.submit-btn.jsx-7e5101c32fcbacf1{width:100%;margin-top:24px;padding:14px;background:#0070f3;color:#fff;border:none;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:18px;cursor:pointer;font-weight:500}.submit-btn.jsx-7e5101c32fcbacf1:hover:not(:disabled){background:#005bb5}.submit-btn.jsx-7e5101c32fcbacf1:disabled{background:#6c757d;cursor:not-allowed}.login-link.jsx-7e5101c32fcbacf1{text-align:center;margin-top:20px;color:#666}.login-link.jsx-7e5101c32fcbacf1 a.jsx-7e5101c32fcbacf1{color:#0070f3;text-decoration:none;font-weight:500}.login-link.jsx-7e5101c32fcbacf1 a.jsx-7e5101c32fcbacf1:hover{text-decoration:underline}footer.jsx-7e5101c32fcbacf1{margin-top:30px;color:#777;font-size:14px;text-align:center}footer.jsx-7e5101c32fcbacf1 a.jsx-7e5101c32fcbacf1{color:#0070f3;text-decoration:none}footer.jsx-7e5101c32fcbacf1 a.jsx-7e5101c32fcbacf1:hover{text-decoration:underline}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/register.js\n");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/register.js"));
module.exports = __webpack_exports__;

})();