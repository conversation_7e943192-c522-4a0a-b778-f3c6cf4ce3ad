"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/register";
exports.ids = ["pages/register"];
exports.modules = {

/***/ "./pages/register.js":
/*!***************************!*\
  !*** ./pages/register.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Register),\n/* harmony export */   \"getServerSideProps\": () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function fetchGoogleUser(email) {\n    // 刷新 token\n    const tokenRes = await fetch(\"https://oauth2.googleapis.com/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n            client_id: process.env.GOOGLE_CLIENT_ID,\n            client_secret: process.env.GOOGLE_CLIENT_SECRET,\n            refresh_token: process.env.GOOGLE_REFRESH_TOKEN,\n            grant_type: \"refresh_token\"\n        })\n    });\n    if (!tokenRes.ok) return null;\n    const { access_token  } = await tokenRes.json();\n    // 查询 Directory\n    const userRes = await fetch(`https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(email)}`, {\n        headers: {\n            Authorization: `Bearer ${access_token}`\n        }\n    });\n    if (!userRes.ok) return null;\n    return await userRes.json();\n}\nasync function getServerSideProps({ req  }) {\n    const cookies = (0,cookie__WEBPACK_IMPORTED_MODULE_3__.parse)(req.headers.cookie || \"\");\n    const oauthUsername = cookies.oauthUsername;\n    const trustLevel = parseInt(cookies.oauthTrustLevel || \"0\", 10);\n    // 必须先 OAuth2 且信任等级 ≥ 3\n    if (!oauthUsername || trustLevel < 3) {\n        return {\n            redirect: {\n                destination: \"/\",\n                permanent: false\n            }\n        };\n    }\n    // 构建学生邮箱\n    const rawDom = process.env.EMAIL_DOMAIN;\n    const domain = rawDom.startsWith(\"@\") ? rawDom : \"@\" + rawDom;\n    const studentEmail = oauthUsername.includes(\"@\") ? oauthUsername : `${oauthUsername}${domain}`;\n    // 查询 Google Directory，看用户是否已经存在\n    const googleUser = await fetchGoogleUser(studentEmail);\n    if (googleUser) {\n        // 已存在，直接跳 student-portal\n        return {\n            redirect: {\n                destination: \"/student-portal\",\n                permanent: false\n            }\n        };\n    }\n    return {\n        props: {\n            oauthUsername\n        }\n    };\n}\nfunction Register({ oauthUsername  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-2086a1e9e2beb93d\",\n                    children: \"New Student Registration\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                    lineNumber: 57,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2086a1e9e2beb93d\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2086a1e9e2beb93d\" + \" \" + \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"jsx-2086a1e9e2beb93d\",\n                                children: \"New Student Registration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                method: \"POST\",\n                                action: \"/api/register\",\n                                className: \"jsx-2086a1e9e2beb93d\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"username\",\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: \"Username (Same as your Linux.do username, read‑only):\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"username\",\n                                        name: \"username\",\n                                        value: oauthUsername,\n                                        readOnly: true,\n                                        className: \"jsx-2086a1e9e2beb93d\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"fullName\",\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: \"Full Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"fullName\",\n                                        name: \"fullName\",\n                                        required: true,\n                                        className: \"jsx-2086a1e9e2beb93d\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"semester\",\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: \"Semester:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"semester\",\n                                        name: \"semester\",\n                                        required: true,\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            className: \"jsx-2086a1e9e2beb93d\",\n                                            children: \"Fall 2025\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"program\",\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: \"Program:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"program\",\n                                        name: \"program\",\n                                        required: true,\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            className: \"jsx-2086a1e9e2beb93d\",\n                                            children: \"Master of Computer Science\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: \"Password:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        id: \"password\",\n                                        name: \"password\",\n                                        required: true,\n                                        className: \"jsx-2086a1e9e2beb93d\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"personalEmail\",\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: \"Personal Email:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"personalEmail\",\n                                        name: \"personalEmail\",\n                                        required: true,\n                                        className: \"jsx-2086a1e9e2beb93d\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"jsx-2086a1e9e2beb93d\",\n                                        children: \"Register\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-2086a1e9e2beb93d\",\n                        children: [\n                            \"Powered by\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://www.chatgpt.org.uk/\",\n                                target: \"_blank\",\n                                rel: \"noopener\",\n                                className: \"jsx-2086a1e9e2beb93d\",\n                                children: \"chatgpt.org.uk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\register.js\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2086a1e9e2beb93d\",\n                children: \".container.jsx-2086a1e9e2beb93d{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#f0f4f8;padding:20px}.card.jsx-2086a1e9e2beb93d{background:#fff;max-width:480px;width:100%;padding:40px;-webkit-border-radius:10px;-moz-border-radius:10px;border-radius:10px;-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);-moz-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1)}h1.jsx-2086a1e9e2beb93d{text-align:center;color:#333;margin-bottom:20px}label.jsx-2086a1e9e2beb93d{display:block;margin:15px 0 5px;color:#555}input.jsx-2086a1e9e2beb93d,select.jsx-2086a1e9e2beb93d{width:100%;padding:10px;border:1px solid#ccc;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:16px}input[readOnly].jsx-2086a1e9e2beb93d{background:#eaeaea}button.jsx-2086a1e9e2beb93d{width:100%;margin-top:24px;padding:12px;background:#0070f3;color:#fff;border:none;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:18px;cursor:pointer}button.jsx-2086a1e9e2beb93d:hover{background:#005bb5}footer.jsx-2086a1e9e2beb93d{margin-top:30px;color:#777;font-size:14px}footer.jsx-2086a1e9e2beb93d a.jsx-2086a1e9e2beb93d{color:#0070f3;text-decoration:none}footer.jsx-2086a1e9e2beb93d a.jsx-2086a1e9e2beb93d:hover{text-decoration:underline}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/register.js\n");

/***/ }),

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/register.js"));
module.exports = __webpack_exports__;

})();