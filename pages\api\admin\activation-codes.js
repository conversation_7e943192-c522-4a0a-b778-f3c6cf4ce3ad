// pages/api/admin/activation-codes.js
import {
  generateActivationCode,
  getAllActivationCodes,
  disableActivationCode,
  enableActivationCode,
  deleteActivationCode,
  getActivationCodeStats
} from '../../../lib/activation-codes'

import cookie from 'cookie'

const SESSION_SECRET = process.env.SESSION_SECRET || 'your-secret-key'

function verifyAdmin(req) {
  const cookies = cookie.parse(req.headers.cookie || '')
  const sessionToken = cookies.adminSession

  if (!sessionToken) {
    return false
  }

  try {
    const decoded = Buffer.from(sessionToken, 'base64').toString()
    const [user, timestamp, secret] = decoded.split(':')

    if (user !== 'admin' || secret !== SESSION_SECRET) {
      return false
    }

    // 检查session是否过期（24小时）
    const sessionTime = parseInt(timestamp)
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24小时

    if (now - sessionTime > maxAge) {
      return false
    }

    return true
  } catch (error) {
    return false
  }
}

export default async function handler(req, res) {
  // 验证管理员权限
  if (!verifyAdmin(req)) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  const { method } = req

  switch (method) {
    case 'GET':
      // 获取所有激活码和统计信息
      try {
        const codes = getAllActivationCodes()
        const stats = getActivationCodeStats()
        res.status(200).json({ codes, stats })
      } catch (error) {
        res.status(500).json({ error: 'Failed to fetch activation codes' })
      }
      break

    case 'POST':
      // 生成新的激活码
      try {
        const { description, expiresInDays } = req.body
        const newCode = generateActivationCode(description, expiresInDays)
        
        if (newCode) {
          res.status(201).json({ success: true, code: newCode })
        } else {
          res.status(500).json({ error: 'Failed to generate activation code' })
        }
      } catch (error) {
        res.status(500).json({ error: 'Failed to generate activation code' })
      }
      break

    case 'PUT':
      // 更新激活码状态（启用/禁用）
      try {
        const { codeId, action } = req.body
        
        let success = false
        if (action === 'disable') {
          success = disableActivationCode(codeId)
        } else if (action === 'enable') {
          success = enableActivationCode(codeId)
        }
        
        if (success) {
          res.status(200).json({ success: true })
        } else {
          res.status(400).json({ error: 'Failed to update activation code' })
        }
      } catch (error) {
        res.status(500).json({ error: 'Failed to update activation code' })
      }
      break

    case 'DELETE':
      // 删除激活码
      try {
        const { codeId } = req.body
        const success = deleteActivationCode(codeId)
        
        if (success) {
          res.status(200).json({ success: true })
        } else {
          res.status(400).json({ error: 'Failed to delete activation code' })
        }
      } catch (error) {
        res.status(500).json({ error: 'Failed to delete activation code' })
      }
      break

    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE'])
      res.status(405).end(`Method ${method} Not Allowed`)
  }
}
