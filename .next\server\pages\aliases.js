"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/aliases";
exports.ids = ["pages/aliases"];
exports.modules = {

/***/ "./pages/aliases.js":
/*!**************************!*\
  !*** ./pages/aliases.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AliasesPage),\n/* harmony export */   \"getServerSideProps\": () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n// pages/aliases.js\n\n\n\n\n\n// 刷新 OAuth2 token\nasync function fetchGoogleToken() {\n    const res = await fetch(\"https://oauth2.googleapis.com/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n            client_id: process.env.GOOGLE_CLIENT_ID,\n            client_secret: process.env.GOOGLE_CLIENT_SECRET,\n            refresh_token: process.env.GOOGLE_REFRESH_TOKEN,\n            grant_type: \"refresh_token\"\n        })\n    });\n    if (!res.ok) return null;\n    const { access_token  } = await res.json();\n    return access_token;\n}\nasync function getServerSideProps({ req  }) {\n    const cookies = (0,cookie__WEBPACK_IMPORTED_MODULE_3__.parse)(req.headers.cookie || \"\");\n    const oauthUsername = cookies.oauthUsername;\n    const trustLevel = parseInt(cookies.oauthTrustLevel || \"0\", 10);\n    if (!oauthUsername || trustLevel < 3) {\n        return {\n            redirect: {\n                destination: \"/\",\n                permanent: false\n            }\n        };\n    }\n    const rawDom = process.env.EMAIL_DOMAIN;\n    const domain = rawDom.startsWith(\"@\") ? rawDom.slice(1) : rawDom;\n    const studentEmail = oauthUsername.includes(\"@\") ? oauthUsername : `${oauthUsername}@${domain}`;\n    const token = await fetchGoogleToken();\n    let aliases = [];\n    if (token) {\n        const listRes = await fetch(`https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(studentEmail)}/aliases`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (listRes.ok) {\n            const data = await listRes.json();\n            aliases = (data.aliases || []).map((a)=>a.alias);\n        }\n    }\n    return {\n        props: {\n            aliases,\n            studentEmail,\n            emailDomain: domain\n        }\n    };\n}\nfunction AliasesPage({ aliases , studentEmail , emailDomain  }) {\n    const [list, setList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(aliases);\n    const [suffix, setSuffix] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    // 删除别名\n    const handleDelete = async (alias)=>{\n        if (!confirm(`Delete alias ${alias}?`)) return;\n        const res = await fetch(\"/api/aliases/delete\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                alias\n            })\n        });\n        if (res.ok) {\n            setList(list.filter((a)=>a !== alias));\n        } else {\n            alert(\"Delete failed\");\n        }\n    };\n    // 新增别名\n    const handleAdd = async (e)=>{\n        e.preventDefault();\n        if (!suffix) {\n            return alert(\"Please enter the alias suffix.\");\n        }\n        // 前端前缀已固化为 chatgpt_\n        const fullAlias = `chatgpt_${suffix}@${emailDomain}`;\n        const res = await fetch(\"/api/aliases/add\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                alias: fullAlias\n            })\n        });\n        if (res.ok) {\n            setList([\n                ...list,\n                fullAlias\n            ]);\n            setSuffix(\"\");\n        } else {\n            const text = await res.text();\n            alert(\"Add failed: \" + text);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-114c49c069a6f374\",\n                    children: \"Manage Email Aliases\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                    lineNumber: 103,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-114c49c069a6f374\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-114c49c069a6f374\" + \" \" + \"school-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"48\",\n                                height: \"48\",\n                                viewBox: \"0 0 48 48\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"jsx-114c49c069a6f374\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"24\",\n                                        cy: \"24\",\n                                        r: \"22\",\n                                        fill: \"#0062cc\",\n                                        className: \"jsx-114c49c069a6f374\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                        x: \"14\",\n                                        y: \"14\",\n                                        width: \"20\",\n                                        height: \"12\",\n                                        rx: \"2\",\n                                        fill: \"#fff\",\n                                        className: \"jsx-114c49c069a6f374\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M14,26 L24,36 L34,26 Z\",\n                                        fill: \"#fff\",\n                                        className: \"jsx-114c49c069a6f374\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                        x: \"24\",\n                                        y: \"22\",\n                                        textAnchor: \"middle\",\n                                        fontSize: \"12\",\n                                        fill: \"#0062cc\",\n                                        fontFamily: \"Arial, sans-serif\",\n                                        className: \"jsx-114c49c069a6f374\",\n                                        children: \"CU\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-114c49c069a6f374\" + \" \" + \"school-text\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"jsx-114c49c069a6f374\",\n                                        children: \"ChatGPT University\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-114c49c069a6f374\",\n                                        children: \"Manage Email Aliases\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-114c49c069a6f374\" + \" \" + \"info\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-114c49c069a6f374\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"jsx-114c49c069a6f374\",\n                                    children: \"Student Email:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                studentEmail\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-114c49c069a6f374\" + \" \" + \"aliases\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"jsx-114c49c069a6f374\",\n                                children: \"Existing Aliases\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            list.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-114c49c069a6f374\",\n                                children: \"No aliases yet.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 126,\n                                columnNumber: 33\n                            }, this),\n                            list.map((alias)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-114c49c069a6f374\" + \" \" + \"alias-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-114c49c069a6f374\",\n                                            children: alias\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleDelete(alias),\n                                            className: \"jsx-114c49c069a6f374\",\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, alias, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleAdd,\n                        className: \"jsx-114c49c069a6f374\" + \" \" + \"add-form\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-114c49c069a6f374\",\n                                children: \"chatgpt_\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                value: suffix,\n                                onChange: (e)=>setSuffix(e.target.value.trim()),\n                                placeholder: \"your-alias-suffix\",\n                                className: \"jsx-114c49c069a6f374\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-114c49c069a6f374\",\n                                children: [\n                                    \"@\",\n                                    emailDomain\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"jsx-114c49c069a6f374\",\n                                children: \"Add Alias\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            textAlign: \"center\",\n                            marginTop: 20\n                        },\n                        className: \"jsx-114c49c069a6f374\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/student-portal\",\n                            className: \"jsx-114c49c069a6f374\",\n                            children: \"← Back to Portal\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\aliases.js\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"114c49c069a6f374\",\n                children: \".container.jsx-114c49c069a6f374{max-width:600px;margin:40px auto;padding:0 20px}.school-header.jsx-114c49c069a6f374{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px;margin-bottom:24px}.school-text.jsx-114c49c069a6f374 h1.jsx-114c49c069a6f374{margin:0;font-size:24px;color:#333}.school-text.jsx-114c49c069a6f374 h2.jsx-114c49c069a6f374{margin:0;font-size:18px;color:#555}.info.jsx-114c49c069a6f374{background:#f7f7f7;padding:16px;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;margin-bottom:20px}.aliases.jsx-114c49c069a6f374{background:#fff;padding:16px;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;margin-bottom:20px;-webkit-box-shadow:0 2px 6px rgba(0,0,0,.1);-moz-box-shadow:0 2px 6px rgba(0,0,0,.1);box-shadow:0 2px 6px rgba(0,0,0,.1)}.alias-item.jsx-114c49c069a6f374{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:8px 0}.alias-item.jsx-114c49c069a6f374+.alias-item.jsx-114c49c069a6f374{border-top:1px solid#eee}.alias-item.jsx-114c49c069a6f374 button.jsx-114c49c069a6f374{background:#dc3545;color:#fff;border:none;padding:4px 8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;cursor:pointer}.alias-item.jsx-114c49c069a6f374 button.jsx-114c49c069a6f374:hover{background:#c82333}.add-form.jsx-114c49c069a6f374{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:8px;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.add-form.jsx-114c49c069a6f374 span.jsx-114c49c069a6f374{white-space:nowrap;font-weight:bold}.add-form.jsx-114c49c069a6f374 input.jsx-114c49c069a6f374{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;padding:8px;border:1px solid#ccc;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.add-form.jsx-114c49c069a6f374 button.jsx-114c49c069a6f374{padding:8px 16px;background:#28a745;color:#fff;border:none;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;cursor:pointer}.add-form.jsx-114c49c069a6f374 button.jsx-114c49c069a6f374:hover{background:#218838}a.jsx-114c49c069a6f374{color:#0070f3;text-decoration:none}a.jsx-114c49c069a6f374:hover{text-decoration:underline}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/aliases.js\n");

/***/ }),

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/aliases.js"));
module.exports = __webpack_exports__;

})();