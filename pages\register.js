import Head from 'next/head'
import { useState } from 'react'

// 新的注册页面不需要服务端预处理，直接渲染
export async function getServerSideProps() {
  return { props: {} }
}

export default function Register() {
  const [formData, setFormData] = useState({
    activationCode: '',
    username: '',
    fullName: '',
    semester: 'Fall 2025',
    program: 'Master of Computer Science',
    password: '',
    personalEmail: ''
  })
  const [isValidating, setIsValidating] = useState(false)
  const [codeValidation, setCodeValidation] = useState(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitMessage, setSubmitMessage] = useState('')

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // 清除激活码验证状态当用户修改激活码时
    if (name === 'activationCode') {
      setCodeValidation(null)
    }
  }

  const validateActivationCode = async () => {
    if (!formData.activationCode.trim()) {
      setCodeValidation({ valid: false, message: '请输入激活码' })
      return
    }

    setIsValidating(true)
    try {
      const response = await fetch('/api/validate-activation-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: formData.activationCode })
      })

      const result = await response.json()
      setCodeValidation(result)
    } catch (error) {
      setCodeValidation({ valid: false, message: '验证失败，请稍后重试' })
    } finally {
      setIsValidating(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // 首先验证激活码
    if (!codeValidation || !codeValidation.valid) {
      setSubmitMessage('请先验证激活码')
      return
    }

    setIsSubmitting(true)
    setSubmitMessage('')

    try {
      const response = await fetch('/api/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (response.ok) {
        setSubmitMessage('注册成功！正在跳转到登录页面...')
        setTimeout(() => {
          window.location.href = '/login'
        }, 2000)
      } else {
        setSubmitMessage(result.message || '注册失败，请稍后重试')
      }
    } catch (error) {
      setSubmitMessage('注册失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <Head><title>学生注册 - Great Heights School</title></Head>
      <div className="container">
        <div className="card">
          <h1>学生注册</h1>
          <form onSubmit={handleSubmit}>
            <div className="activation-code-section">
              <label htmlFor="activationCode">激活码 *</label>
              <div className="code-input-group">
                <input
                  type="text"
                  id="activationCode"
                  name="activationCode"
                  value={formData.activationCode}
                  onChange={handleInputChange}
                  placeholder="请输入激活码"
                  required
                />
                <button
                  type="button"
                  onClick={validateActivationCode}
                  disabled={isValidating || !formData.activationCode.trim()}
                  className="validate-btn"
                >
                  {isValidating ? '验证中...' : '验证'}
                </button>
              </div>
              {codeValidation && (
                <div className={`validation-message ${codeValidation.valid ? 'valid' : 'invalid'}`}>
                  {codeValidation.message}
                </div>
              )}
            </div>

            <label htmlFor="username">用户名 *</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="请输入用户名"
              required
            />

            <label htmlFor="fullName">姓名 *</label>
            <input
              type="text"
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              placeholder="请输入您的姓名"
              required
            />

            <label htmlFor="semester">学期 *</label>
            <select
              id="semester"
              name="semester"
              value={formData.semester}
              onChange={handleInputChange}
              required
            >
              <option value="Fall 2025">Fall 2025</option>
              <option value="Spring 2025">Spring 2025</option>
              <option value="Summer 2025">Summer 2025</option>
            </select>

            <label htmlFor="program">专业 *</label>
            <select
              id="program"
              name="program"
              value={formData.program}
              onChange={handleInputChange}
              required
            >
              <option value="Master of Computer Science">计算机科学硕士</option>
              <option value="Master of Business Administration">工商管理硕士</option>
              <option value="Master of Education">教育学硕士</option>
            </select>

            <label htmlFor="password">密码 *</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="请设置密码"
              required
            />

            <label htmlFor="personalEmail">个人邮箱 *</label>
            <input
              type="email"
              id="personalEmail"
              name="personalEmail"
              value={formData.personalEmail}
              onChange={handleInputChange}
              placeholder="请输入您的个人邮箱"
              required
            />

            {submitMessage && (
              <div className={`submit-message ${submitMessage.includes('成功') ? 'success' : 'error'}`}>
                {submitMessage}
              </div>
            )}

            <button
              type="submit"
              disabled={isSubmitting || !codeValidation?.valid}
              className="submit-btn"
            >
              {isSubmitting ? '注册中...' : '注册'}
            </button>
          </form>

          <div className="login-link">
            已有账号？<a href="/login">点击登录</a>
          </div>
        </div>
        <footer>
          Powered by{' '}
          <a href="#" target="_blank" rel="noopener">
            Garbage Human Studio
          </a>
        </footer>
      </div>
      <style jsx>{`
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #f0f4f8;
          padding: 20px;
        }
        .card {
          background: #fff;
          max-width: 500px;
          width: 100%;
          padding: 40px;
          border-radius: 10px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
          text-align: center;
          color: #333;
          margin-bottom: 30px;
          font-size: 28px;
        }
        label {
          display: block;
          margin: 15px 0 5px;
          color: #555;
          font-weight: 500;
        }
        input, select {
          width: 100%;
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 16px;
          box-sizing: border-box;
        }
        input:focus, select:focus {
          outline: none;
          border-color: #0070f3;
          box-shadow: 0 0 0 2px rgba(0,112,243,0.1);
        }
        .activation-code-section {
          margin-bottom: 20px;
        }
        .code-input-group {
          display: flex;
          gap: 10px;
        }
        .code-input-group input {
          flex: 1;
        }
        .validate-btn {
          padding: 12px 20px;
          background: #28a745;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          white-space: nowrap;
        }
        .validate-btn:hover:not(:disabled) {
          background: #218838;
        }
        .validate-btn:disabled {
          background: #6c757d;
          cursor: not-allowed;
        }
        .validation-message {
          margin-top: 8px;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 14px;
        }
        .validation-message.valid {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }
        .validation-message.invalid {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }
        .submit-message {
          margin: 15px 0;
          padding: 12px;
          border-radius: 6px;
          text-align: center;
          font-weight: 500;
        }
        .submit-message.success {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }
        .submit-message.error {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }
        .submit-btn {
          width: 100%;
          margin-top: 24px;
          padding: 14px;
          background: #0070f3;
          color: #fff;
          border: none;
          border-radius: 6px;
          font-size: 18px;
          cursor: pointer;
          font-weight: 500;
        }
        .submit-btn:hover:not(:disabled) {
          background: #005bb5;
        }
        .submit-btn:disabled {
          background: #6c757d;
          cursor: not-allowed;
        }
        .login-link {
          text-align: center;
          margin-top: 20px;
          color: #666;
        }
        .login-link a {
          color: #0070f3;
          text-decoration: none;
          font-weight: 500;
        }
        .login-link a:hover {
          text-decoration: underline;
        }
        footer {
          margin-top: 30px;
          color: #777;
          font-size: 14px;
          text-align: center;
        }
        footer a {
          color: #0070f3;
          text-decoration: none;
        }
        footer a:hover {
          text-decoration: underline;
        }
      `}</style>
    </>
  )
}
