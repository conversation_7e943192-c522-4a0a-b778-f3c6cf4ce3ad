"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/google/callback";
exports.ids = ["pages/api/auth/google/callback"];
exports.modules = {

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "(api)/./pages/api/auth/google/callback.js":
/*!*******************************************!*\
  !*** ./pages/api/auth/google/callback.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_0__);\n// pages/api/auth/google/callback.js\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    const { code , state , error  } = req.query;\n    // 检查是否有错误\n    if (error) {\n        console.error(\"Google OAuth error:\", error);\n        return res.redirect(\"/?error=oauth_error\");\n    }\n    // 验证state参数\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_0___default().parse(req.headers.cookie || \"\");\n    if (cookies.googleOAuthState !== state) {\n        console.error(\"Invalid state parameter\");\n        return res.redirect(\"/?error=invalid_state\");\n    }\n    if (!code) {\n        console.error(\"No authorization code received\");\n        return res.redirect(\"/?error=no_code\");\n    }\n    try {\n        // 交换授权码获取访问令牌\n        const tokenResponse = await fetch(\"https://oauth2.googleapis.com/token\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            },\n            body: new URLSearchParams({\n                client_id: process.env.GOOGLE_OAUTH_CLIENT_ID,\n                client_secret: process.env.GOOGLE_OAUTH_CLIENT_SECRET,\n                code,\n                grant_type: \"authorization_code\",\n                redirect_uri: process.env.GOOGLE_OAUTH_REDIRECT_URI || `${process.env.NEXTAUTH_URL || \"http://localhost:3000\"}/api/auth/google/callback`\n            })\n        });\n        if (!tokenResponse.ok) {\n            const errorText = await tokenResponse.text();\n            console.error(\"Token exchange error:\", errorText);\n            return res.redirect(\"/?error=token_error\");\n        }\n        const tokenData = await tokenResponse.json();\n        const { access_token , id_token  } = tokenData;\n        // 获取用户信息\n        const userResponse = await fetch(\"https://www.googleapis.com/oauth2/v2/userinfo\", {\n            headers: {\n                Authorization: `Bearer ${access_token}`\n            }\n        });\n        if (!userResponse.ok) {\n            console.error(\"Failed to fetch user info\");\n            return res.redirect(\"/?error=user_info_error\");\n        }\n        const userData = await userResponse.json();\n        const { email , name , picture  } = userData;\n        // 验证邮箱后缀\n        const allowedDomain = process.env.EMAIL_DOMAIN || \"ghs.edu.kg\";\n        const domain = allowedDomain.startsWith(\"@\") ? allowedDomain.slice(1) : allowedDomain;\n        if (!email.endsWith(`@${domain}`)) {\n            console.log(`Login attempt with invalid email domain: ${email}`);\n            return res.redirect(`/?error=invalid_domain&domain=${domain}`);\n        }\n        // 检查用户是否在Google Workspace中存在\n        const adminTokenRes = await fetch(\"https://oauth2.googleapis.com/token\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            },\n            body: new URLSearchParams({\n                client_id: process.env.GOOGLE_CLIENT_ID,\n                client_secret: process.env.GOOGLE_CLIENT_SECRET,\n                refresh_token: process.env.GOOGLE_REFRESH_TOKEN,\n                grant_type: \"refresh_token\"\n            })\n        });\n        if (adminTokenRes.ok) {\n            const { access_token: adminToken  } = await adminTokenRes.json();\n            // 检查用户是否在Google Directory中存在\n            const directoryRes = await fetch(`https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(email)}`, {\n                headers: {\n                    Authorization: `Bearer ${adminToken}`\n                }\n            });\n            if (!directoryRes.ok) {\n                console.log(`User not found in Google Directory: ${email}`);\n                return res.redirect(\"/?error=user_not_found\");\n            }\n        }\n        // 设置登录session cookies\n        const sessionCookies = [\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"userEmail\", email, {\n                path: \"/\",\n                httpOnly: true,\n                secure: \"development\" === \"production\",\n                sameSite: \"lax\",\n                maxAge: 24 * 60 * 60 // 24小时\n            }),\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"userName\", name, {\n                path: \"/\",\n                httpOnly: true,\n                secure: \"development\" === \"production\",\n                sameSite: \"lax\",\n                maxAge: 24 * 60 * 60\n            }),\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"userPicture\", picture || \"\", {\n                path: \"/\",\n                httpOnly: true,\n                secure: \"development\" === \"production\",\n                sameSite: \"lax\",\n                maxAge: 24 * 60 * 60\n            }),\n            // 清除OAuth状态cookie\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"googleOAuthState\", \"\", {\n                path: \"/\",\n                expires: new Date(0)\n            })\n        ];\n        res.setHeader(\"Set-Cookie\", sessionCookies);\n        // 登录成功，重定向到学生门户\n        res.redirect(\"/student-portal\");\n    } catch (error) {\n        console.error(\"OAuth callback error:\", error);\n        res.redirect(\"/?error=server_error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/auth/google/callback.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/auth/google/callback.js"));
module.exports = __webpack_exports__;

})();