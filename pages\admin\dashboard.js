import Head from 'next/head'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

export default function AdminDashboard() {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [codes, setCodes] = useState([])
  const [stats, setStats] = useState({})
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newCodeForm, setNewCodeForm] = useState({
    description: '',
    expiresInDays: 30
  })
  const [isCreating, setIsCreating] = useState(false)

  // 检查认证状态
  useEffect(() => {
    checkAuth()
  }, [])

  // 加载激活码数据
  useEffect(() => {
    if (isAuthenticated) {
      loadActivationCodes()
    }
  }, [isAuthenticated])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/admin/auth')
      const result = await response.json()
      
      if (result.authenticated) {
        setIsAuthenticated(true)
      } else {
        router.push('/admin/login')
      }
    } catch (error) {
      router.push('/admin/login')
    } finally {
      setIsLoading(false)
    }
  }

  const loadActivationCodes = async () => {
    try {
      const response = await fetch('/api/admin/activation-codes')
      if (response.ok) {
        const data = await response.json()
        setCodes(data.codes)
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Failed to load activation codes:', error)
    }
  }

  const handleCreateCode = async (e) => {
    e.preventDefault()
    setIsCreating(true)

    try {
      const response = await fetch('/api/admin/activation-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newCodeForm),
      })

      if (response.ok) {
        setNewCodeForm({ description: '', expiresInDays: 30 })
        setShowCreateForm(false)
        loadActivationCodes() // 重新加载数据
      } else {
        alert('创建激活码失败')
      }
    } catch (error) {
      alert('创建激活码失败')
    } finally {
      setIsCreating(false)
    }
  }

  const handleToggleCode = async (codeId, action) => {
    try {
      const response = await fetch('/api/admin/activation-codes', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ codeId, action }),
      })

      if (response.ok) {
        loadActivationCodes() // 重新加载数据
      } else {
        alert('操作失败')
      }
    } catch (error) {
      alert('操作失败')
    }
  }

  const handleDeleteCode = async (codeId) => {
    if (!confirm('确定要删除这个激活码吗？')) {
      return
    }

    try {
      const response = await fetch('/api/admin/activation-codes', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ codeId }),
      })

      if (response.ok) {
        loadActivationCodes() // 重新加载数据
      } else {
        alert('删除失败')
      }
    } catch (error) {
      alert('删除失败')
    }
  }

  const handleLogout = async () => {
    try {
      await fetch('/api/admin/auth', { method: 'DELETE' })
      router.push('/admin/login')
    } catch (error) {
      router.push('/admin/login')
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const getStatusBadge = (code) => {
    if (code.isUsed) return { text: '已使用', class: 'used' }
    if (!code.isActive) return { text: '已禁用', class: 'disabled' }
    if (new Date() > new Date(code.expiresAt)) return { text: '已过期', class: 'expired' }
    return { text: '可用', class: 'available' }
  }

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <p>加载中...</p>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // 会被重定向到登录页
  }

  return (
    <>
      <Head>
        <title>激活码管理 - Great Heights School</title>
      </Head>
      <div className="container">
        <header className="header">
          <div className="header-content">
            <h1>激活码管理系统</h1>
            <div className="header-actions">
              <button 
                onClick={() => setShowCreateForm(true)}
                className="btn btn-primary"
              >
                生成激活码
              </button>
              <button 
                onClick={handleLogout}
                className="btn btn-secondary"
              >
                登出
              </button>
            </div>
          </div>
        </header>

        <div className="stats-grid">
          <div className="stat-card">
            <h3>总计</h3>
            <div className="stat-number">{stats.total || 0}</div>
          </div>
          <div className="stat-card">
            <h3>可用</h3>
            <div className="stat-number available">{stats.available || 0}</div>
          </div>
          <div className="stat-card">
            <h3>已使用</h3>
            <div className="stat-number used">{stats.used || 0}</div>
          </div>
          <div className="stat-card">
            <h3>已过期</h3>
            <div className="stat-number expired">{stats.expired || 0}</div>
          </div>
        </div>

        {showCreateForm && (
          <div className="modal-overlay" onClick={() => setShowCreateForm(false)}>
            <div className="modal" onClick={(e) => e.stopPropagation()}>
              <h2>生成新激活码</h2>
              <form onSubmit={handleCreateCode}>
                <div className="form-group">
                  <label>描述</label>
                  <input
                    type="text"
                    value={newCodeForm.description}
                    onChange={(e) => setNewCodeForm({...newCodeForm, description: e.target.value})}
                    placeholder="激活码用途描述（可选）"
                  />
                </div>
                <div className="form-group">
                  <label>有效期（天）</label>
                  <input
                    type="number"
                    value={newCodeForm.expiresInDays}
                    onChange={(e) => setNewCodeForm({...newCodeForm, expiresInDays: parseInt(e.target.value)})}
                    min="1"
                    max="365"
                    required
                  />
                </div>
                <div className="form-actions">
                  <button 
                    type="button" 
                    onClick={() => setShowCreateForm(false)}
                    className="btn btn-secondary"
                  >
                    取消
                  </button>
                  <button 
                    type="submit" 
                    disabled={isCreating}
                    className="btn btn-primary"
                  >
                    {isCreating ? '生成中...' : '生成'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        <div className="codes-section">
          <h2>激活码列表</h2>
          {codes.length === 0 ? (
            <div className="empty-state">
              <p>暂无激活码</p>
            </div>
          ) : (
            <div className="codes-table">
              <div className="table-header">
                <div>激活码</div>
                <div>状态</div>
                <div>创建时间</div>
                <div>过期时间</div>
                <div>使用者</div>
                <div>操作</div>
              </div>
              {codes.map((code) => {
                const status = getStatusBadge(code)
                return (
                  <div key={code.id} className="table-row">
                    <div className="code-cell">
                      <span className="code-text">{code.code}</span>
                      {code.description && (
                        <span className="code-desc">{code.description}</span>
                      )}
                    </div>
                    <div>
                      <span className={`status-badge ${status.class}`}>
                        {status.text}
                      </span>
                    </div>
                    <div>{formatDate(code.createdAt)}</div>
                    <div>{formatDate(code.expiresAt)}</div>
                    <div>{code.usedBy || '-'}</div>
                    <div className="actions">
                      {!code.isUsed && (
                        <button
                          onClick={() => handleToggleCode(code.id, code.isActive ? 'disable' : 'enable')}
                          className={`btn btn-sm ${code.isActive ? 'btn-warning' : 'btn-success'}`}
                        >
                          {code.isActive ? '禁用' : '启用'}
                        </button>
                      )}
                      <button
                        onClick={() => handleDeleteCode(code.id)}
                        className="btn btn-sm btn-danger"
                      >
                        删除
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        .loading-container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #f5f5f5;
        }
        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid #4285f4;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 16px;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .container {
          min-height: 100vh;
          background: #f5f5f5;
          padding: 20px;
        }
        .header {
          background: #fff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          margin-bottom: 20px;
        }
        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          max-width: 1200px;
          margin: 0 auto;
        }
        .header h1 {
          margin: 0;
          color: #333;
          font-size: 24px;
        }
        .header-actions {
          display: flex;
          gap: 12px;
        }
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-bottom: 24px;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
        }
        .stat-card {
          background: #fff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          text-align: center;
        }
        .stat-card h3 {
          margin: 0 0 8px 0;
          color: #666;
          font-size: 14px;
          font-weight: 500;
        }
        .stat-number {
          font-size: 32px;
          font-weight: 700;
          color: #333;
        }
        .stat-number.available { color: #28a745; }
        .stat-number.used { color: #6c757d; }
        .stat-number.expired { color: #dc3545; }
        .codes-section {
          background: #fff;
          padding: 24px;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          max-width: 1200px;
          margin: 0 auto;
        }
        .codes-section h2 {
          margin: 0 0 20px 0;
          color: #333;
          font-size: 20px;
        }
        .empty-state {
          text-align: center;
          padding: 40px;
          color: #666;
        }
        .codes-table {
          overflow-x: auto;
        }
        .table-header, .table-row {
          display: grid;
          grid-template-columns: 2fr 1fr 1.5fr 1.5fr 1.5fr 1fr;
          gap: 16px;
          padding: 12px 0;
          border-bottom: 1px solid #eee;
          align-items: center;
        }
        .table-header {
          font-weight: 600;
          color: #333;
          background: #f8f9fa;
          padding: 16px 0;
          border-radius: 4px;
        }
        .code-cell {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }
        .code-text {
          font-family: monospace;
          font-weight: 600;
          color: #333;
        }
        .code-desc {
          font-size: 12px;
          color: #666;
        }
        .status-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
        .status-badge.available {
          background: #d4edda;
          color: #155724;
        }
        .status-badge.used {
          background: #e2e3e5;
          color: #383d41;
        }
        .status-badge.disabled {
          background: #f8d7da;
          color: #721c24;
        }
        .status-badge.expired {
          background: #fff3cd;
          color: #856404;
        }
        .actions {
          display: flex;
          gap: 8px;
        }
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s ease;
        }
        .btn-primary {
          background: #4285f4;
          color: #fff;
        }
        .btn-primary:hover {
          background: #3367d6;
        }
        .btn-secondary {
          background: #6c757d;
          color: #fff;
        }
        .btn-secondary:hover {
          background: #545b62;
        }
        .btn-success {
          background: #28a745;
          color: #fff;
        }
        .btn-success:hover {
          background: #218838;
        }
        .btn-warning {
          background: #ffc107;
          color: #212529;
        }
        .btn-warning:hover {
          background: #e0a800;
        }
        .btn-danger {
          background: #dc3545;
          color: #fff;
        }
        .btn-danger:hover {
          background: #c82333;
        }
        .btn-sm {
          padding: 4px 8px;
          font-size: 12px;
        }
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0,0,0,0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }
        .modal {
          background: #fff;
          padding: 24px;
          border-radius: 8px;
          width: 90%;
          max-width: 400px;
        }
        .modal h2 {
          margin: 0 0 20px 0;
          color: #333;
        }
        .form-group {
          margin-bottom: 16px;
        }
        .form-group label {
          display: block;
          margin-bottom: 4px;
          color: #333;
          font-weight: 500;
        }
        .form-group input {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          box-sizing: border-box;
        }
        .form-group input:focus {
          outline: none;
          border-color: #4285f4;
        }
        .form-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
        }
      `}</style>
    </>
  )
}
