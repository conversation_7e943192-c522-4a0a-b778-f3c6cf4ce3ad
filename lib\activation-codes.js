// lib/activation-codes.js
import fs from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

const CODES_FILE = path.join(process.cwd(), 'data', 'activation-codes.json')

// 确保数据目录存在
function ensureDataDir() {
  const dataDir = path.dirname(CODES_FILE)
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true })
  }
}

// 读取激活码数据
function readCodesData() {
  ensureDataDir()
  if (!fs.existsSync(CODES_FILE)) {
    return []
  }
  try {
    const data = fs.readFileSync(CODES_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading activation codes:', error)
    return []
  }
}

// 写入激活码数据
function writeCodesData(codes) {
  ensureDataDir()
  try {
    fs.writeFileSync(CODES_FILE, JSON.stringify(codes, null, 2))
    return true
  } catch (error) {
    console.error('Error writing activation codes:', error)
    return false
  }
}

// 生成激活码
export function generateActivationCode(description = '', expiresInDays = 30) {
  const codes = readCodesData()
  const code = uuidv4().replace(/-/g, '').substring(0, 16).toUpperCase()
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + expiresInDays)
  
  const newCode = {
    id: uuidv4(),
    code,
    description,
    createdAt: new Date().toISOString(),
    expiresAt: expiresAt.toISOString(),
    isUsed: false,
    usedAt: null,
    usedBy: null,
    isActive: true
  }
  
  codes.push(newCode)
  
  if (writeCodesData(codes)) {
    return newCode
  }
  return null
}

// 验证激活码
export function validateActivationCode(code) {
  const codes = readCodesData()
  const activationCode = codes.find(c => c.code === code.toUpperCase())
  
  if (!activationCode) {
    return { valid: false, error: 'INVALID_CODE' }
  }
  
  if (!activationCode.isActive) {
    return { valid: false, error: 'DISABLED_CODE' }
  }
  
  if (activationCode.isUsed) {
    return { valid: false, error: 'USED_CODE' }
  }
  
  if (new Date() > new Date(activationCode.expiresAt)) {
    return { valid: false, error: 'EXPIRED_CODE' }
  }
  
  return { valid: true, code: activationCode }
}

// 使用激活码
export function useActivationCode(code, userEmail) {
  const codes = readCodesData()
  const codeIndex = codes.findIndex(c => c.code === code.toUpperCase())
  
  if (codeIndex === -1) {
    return false
  }
  
  const validation = validateActivationCode(code)
  if (!validation.valid) {
    return false
  }
  
  codes[codeIndex].isUsed = true
  codes[codeIndex].usedAt = new Date().toISOString()
  codes[codeIndex].usedBy = userEmail
  
  return writeCodesData(codes)
}

// 获取所有激活码
export function getAllActivationCodes() {
  return readCodesData()
}

// 禁用激活码
export function disableActivationCode(codeId) {
  const codes = readCodesData()
  const codeIndex = codes.findIndex(c => c.id === codeId)
  
  if (codeIndex === -1) {
    return false
  }
  
  codes[codeIndex].isActive = false
  return writeCodesData(codes)
}

// 启用激活码
export function enableActivationCode(codeId) {
  const codes = readCodesData()
  const codeIndex = codes.findIndex(c => c.id === codeId)
  
  if (codeIndex === -1) {
    return false
  }
  
  codes[codeIndex].isActive = true
  return writeCodesData(codes)
}

// 删除激活码
export function deleteActivationCode(codeId) {
  const codes = readCodesData()
  const filteredCodes = codes.filter(c => c.id !== codeId)
  
  if (filteredCodes.length === codes.length) {
    return false // 没有找到要删除的代码
  }
  
  return writeCodesData(filteredCodes)
}

// 获取激活码统计信息
export function getActivationCodeStats() {
  const codes = readCodesData()
  const total = codes.length
  const active = codes.filter(c => c.isActive).length
  const used = codes.filter(c => c.isUsed).length
  const expired = codes.filter(c => new Date() > new Date(c.expiresAt)).length
  const available = codes.filter(c => 
    c.isActive && 
    !c.isUsed && 
    new Date() <= new Date(c.expiresAt)
  ).length
  
  return {
    total,
    active,
    used,
    expired,
    available
  }
}
