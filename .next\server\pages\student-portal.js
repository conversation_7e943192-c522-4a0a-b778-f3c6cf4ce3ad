"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/student-portal";
exports.ids = ["pages/student-portal"];
exports.modules = {

/***/ "./pages/student-portal.js":
/*!*********************************!*\
  !*** ./pages/student-portal.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentPortal),\n/* harmony export */   \"getServerSideProps\": () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_3__);\n// pages/student-portal.js\n\n\n\n\n// Constants for semester & program\nconst SEMESTER = \"Fall 2025\";\nconst PROGRAM = \"Master of Computer Science\";\n// Helper to refresh token & fetch user from Google Directory\nasync function fetchGoogleUser(email) {\n    const tokenRes = await fetch(\"https://oauth2.googleapis.com/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n            client_id: process.env.GOOGLE_CLIENT_ID,\n            client_secret: process.env.GOOGLE_CLIENT_SECRET,\n            refresh_token: process.env.GOOGLE_REFRESH_TOKEN,\n            grant_type: \"refresh_token\"\n        })\n    });\n    if (!tokenRes.ok) return null;\n    const { access_token  } = await tokenRes.json();\n    const userRes = await fetch(`https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(email)}`, {\n        headers: {\n            Authorization: `Bearer ${access_token}`\n        }\n    });\n    if (!userRes.ok) return null;\n    return await userRes.json();\n}\nasync function getServerSideProps({ req  }) {\n    const cookies = (0,cookie__WEBPACK_IMPORTED_MODULE_3__.parse)(req.headers.cookie || \"\");\n    const oauthUsername = cookies.oauthUsername;\n    const trustLevel = parseInt(cookies.oauthTrustLevel || \"0\", 10);\n    // Must be OAuth2’d and trust_level ≥ 3\n    if (!oauthUsername || trustLevel < 3) {\n        return {\n            redirect: {\n                destination: \"/\",\n                permanent: false\n            }\n        };\n    }\n    // Build the student’s email\n    const rawDom = process.env.EMAIL_DOMAIN;\n    const domain = rawDom.startsWith(\"@\") ? rawDom : \"@\" + rawDom;\n    const studentEmail = oauthUsername.includes(\"@\") ? oauthUsername : `${oauthUsername}${domain}`;\n    // Fetch the Google user – if missing, send them back to register\n    const googleUser = await fetchGoogleUser(studentEmail);\n    if (!googleUser) {\n        return {\n            redirect: {\n                destination: \"/register\",\n                permanent: false\n            }\n        };\n    }\n    // Pull name & recoveryEmail from Google\n    const fullName = `${googleUser.name.givenName} ${googleUser.name.familyName}`;\n    const personalEmail = googleUser.recoveryEmail || \"\";\n    const studentId = cookies.oauthUserId;\n    return {\n        props: {\n            fullName,\n            personalEmail,\n            studentEmail,\n            studentId\n        }\n    };\n}\nfunction StudentPortal({ fullName , personalEmail , studentEmail , studentId  }) {\n    // Pad the forum ID to 6 digits\n    const sid = String(studentId).padStart(6, \"0\");\n    // Links\n    const gmailLink = `https://accounts.google.com/ServiceLogin?Email=${encodeURIComponent(studentEmail)}` + `&continue=https://mail.google.com/mail`;\n    const canvaLink = \"https://www.canva.com/login\";\n    const adobeLink = `https://accounts.adobe.com/`;\n    // Delete handler\n    const handleDelete = async ()=>{\n        if (!confirm(\"Are you sure you want to delete your account?\")) return;\n        const res = await fetch(\"/api/delete-account\", {\n            method: \"POST\"\n        });\n        if (res.ok) {\n            window.location.href = \"/\";\n        } else {\n            alert(\"Failed to delete account.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-510dc291f9b291bf\",\n                    children: \"Student Portal\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                    lineNumber: 98,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-510dc291f9b291bf\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-510dc291f9b291bf\" + \" \" + \"school-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"48\",\n                                height: \"48\",\n                                viewBox: \"0 0 48 48\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"24\",\n                                        cy: \"24\",\n                                        r: \"22\",\n                                        fill: \"#0062cc\",\n                                        className: \"jsx-510dc291f9b291bf\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                        x: \"14\",\n                                        y: \"14\",\n                                        width: \"20\",\n                                        height: \"12\",\n                                        rx: \"2\",\n                                        fill: \"#fff\",\n                                        className: \"jsx-510dc291f9b291bf\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M14,26 L24,36 L34,26 Z\",\n                                        fill: \"#fff\",\n                                        className: \"jsx-510dc291f9b291bf\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                        x: \"24\",\n                                        y: \"22\",\n                                        textAnchor: \"middle\",\n                                        fontSize: \"12\",\n                                        fontFamily: \"Arial, sans-serif\",\n                                        fill: \"#0062cc\",\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"GHS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"school-text\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Great Heights School\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Student Portal\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-510dc291f9b291bf\" + \" \" + \"info\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    fullName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Semester:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    SEMESTER\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Program:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 136,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    PROGRAM\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Student Email:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 137,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    studentEmail\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Personal Email:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    personalEmail\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Student ID:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    sid\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: gmailLink,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://www.gstatic.com/images/branding/product/1x/gmail_48dp.png\",\n                                        alt: \"Student Email\",\n                                        className: \"jsx-510dc291f9b291bf\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Student Email\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/student-card\",\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-510dc291f9b291bf\" + \" \" + \"card-icon\",\n                                        children: \"\\uD83C\\uDF93\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"e‑Student Card\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: adobeLink,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://upload.wikimedia.org/wikipedia/commons/thumb/1/1c/Adobe_Express_logo_RGB_1024px.png/500px-Adobe_Express_logo_RGB_1024px.png\",\n                                        alt: \"Adobe Express\",\n                                        className: \"jsx-510dc291f9b291bf\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Adobe Express\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item empty\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: canvaLink,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://static.canva.com/web/images/8439b51bb7a19f6e65ce1064bc37c197.svg\",\n                                        alt: \"Canva\",\n                                        className: \"jsx-510dc291f9b291bf\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Canva\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/aliases\",\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"48\",\n                                        height: \"48\",\n                                        viewBox: \"0 0 48 48\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"24\",\n                                                cy: \"24\",\n                                                r: \"22\",\n                                                fill: \"#28a745\",\n                                                className: \"jsx-510dc291f9b291bf\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                x1: \"24\",\n                                                y1: \"14\",\n                                                x2: \"24\",\n                                                y2: \"34\",\n                                                stroke: \"#fff\",\n                                                strokeWidth: \"4\",\n                                                className: \"jsx-510dc291f9b291bf\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                x1: \"14\",\n                                                y1: \"24\",\n                                                x2: \"34\",\n                                                y2: \"24\",\n                                                stroke: \"#fff\",\n                                                strokeWidth: \"4\",\n                                                className: \"jsx-510dc291f9b291bf\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Add Email Aliases\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/reset-password\",\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"48\",\n                                        height: \"48\",\n                                        viewBox: \"0 0 48 48\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"12\",\n                                                cy: \"24\",\n                                                r: \"8\",\n                                                fill: \"#ffc107\",\n                                                className: \"jsx-510dc291f9b291bf\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                x: \"18\",\n                                                y: \"20\",\n                                                width: \"20\",\n                                                height: \"8\",\n                                                fill: \"#ffc107\",\n                                                className: \"jsx-510dc291f9b291bf\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                x: \"34\",\n                                                y: \"20\",\n                                                width: \"4\",\n                                                height: \"4\",\n                                                fill: \"#fff\",\n                                                className: \"jsx-510dc291f9b291bf\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                x: \"38\",\n                                                y: \"20\",\n                                                width: \"4\",\n                                                height: \"4\",\n                                                fill: \"#fff\",\n                                                className: \"jsx-510dc291f9b291bf\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-510dc291f9b291bf\",\n                                        children: \"Reset Password\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-510dc291f9b291bf\" + \" \" + \"grid-item empty\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDelete,\n                        className: \"jsx-510dc291f9b291bf\" + \" \" + \"delete-button\",\n                        children: \"Delete My Account\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-510dc291f9b291bf\",\n                        children: [\n                            \"Powered by\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://www.chatgpt.org.uk/\",\n                                target: \"_blank\",\n                                rel: \"noopener\",\n                                className: \"jsx-510dc291f9b291bf\",\n                                children: \"chatgpt.org.uk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"510dc291f9b291bf\",\n                children: \".container.jsx-510dc291f9b291bf{max-width:900px;margin:40px auto;padding:0 20px}.school-header.jsx-510dc291f9b291bf{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px;margin-bottom:24px}.school-text.jsx-510dc291f9b291bf h1.jsx-510dc291f9b291bf{margin:0;font-size:24px;color:#333}.school-text.jsx-510dc291f9b291bf h2.jsx-510dc291f9b291bf{margin:0;font-size:18px;color:#555}.info.jsx-510dc291f9b291bf{background:#f7f7f7;padding:20px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;margin-bottom:30px}.info.jsx-510dc291f9b291bf p.jsx-510dc291f9b291bf{margin:6px 0}.grid.jsx-510dc291f9b291bf{display:grid;grid-template-columns:repeat(auto-fit,minmax(180px,1fr));gap:20px}.grid-item.jsx-510dc291f9b291bf{background:#fff;border:1px solid#ddd;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;padding:24px 12px;text-align:center;-webkit-transition:-webkit-transform.1s,box-shadow.1s;-moz-transition:-moz-transform.1s,box-shadow.1s;-o-transition:-o-transform.1s,box-shadow.1s;transition:-webkit-transform.1s,box-shadow.1s;transition:-moz-transform.1s,box-shadow.1s;transition:-o-transform.1s,box-shadow.1s;transition:transform.1s,box-shadow.1s;text-decoration:none;color:inherit}.grid-item.jsx-510dc291f9b291bf:hover{-webkit-transform:translatey(-4px);-moz-transform:translatey(-4px);-ms-transform:translatey(-4px);-o-transform:translatey(-4px);transform:translatey(-4px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);-moz-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1)}.grid-item.jsx-510dc291f9b291bf img.jsx-510dc291f9b291bf{width:48px;height:48px;margin-bottom:8px;-o-object-fit:contain;object-fit:contain}.card-icon.jsx-510dc291f9b291bf{font-size:48px;margin-bottom:8px}.empty.jsx-510dc291f9b291bf{visibility:hidden}.delete-button.jsx-510dc291f9b291bf{display:block;margin:30px auto 0;padding:10px 20px;background:#dc3545;color:#fff;border:none;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:16px;cursor:pointer}.delete-button.jsx-510dc291f9b291bf:hover{background:#c82333}footer.jsx-510dc291f9b291bf{margin-top:40px;text-align:center;color:#777;font-size:14px}footer.jsx-510dc291f9b291bf a.jsx-510dc291f9b291bf{color:#0070f3;text-decoration:none}footer.jsx-510dc291f9b291bf a.jsx-510dc291f9b291bf:hover{text-decoration:underline}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/student-portal.js\n");

/***/ }),

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/student-portal.js"));
module.exports = __webpack_exports__;

})();