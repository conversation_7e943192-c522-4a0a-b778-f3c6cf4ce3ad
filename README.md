# 🎓 Great Heights School 学生注册系统

基于激活码的学生注册和Google OAuth登录系统，可部署在 Vercel。

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/git/external?repository-url=https://github.com/your-repo/ghs-edu-signup&project-name=ghs-edu-signup)

## ✨ 功能特性

- 🔐 **激活码注册系统** - 需要有效激活码才能注册
- 🌐 **Google OAuth登录** - 仅限 @ghs.edu.kg 邮箱登录
- 👨‍💼 **管理员后台** - 生成、管理、追踪激活码使用状态
- 🏫 **Google Workspace集成** - 自动创建学校邮箱账号
- 📱 **响应式设计** - 支持各种设备访问

---

## 🛠️ 环境变量配置

```env
# 学校邮箱域名
EMAIL_DOMAIN=ghs.edu.kg

# Google Admin SDK (用于创建和管理用户账号)
GOOGLE_CLIENT_ID=YOUR_GOOGLE_ADMIN_CLIENT_ID
GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_ADMIN_CLIENT_SECRET
GOOGLE_REFRESH_TOKEN=YOUR_GOOGLE_ADMIN_REFRESH_TOKEN

# Google OAuth (用于学生登录)
GOOGLE_OAUTH_CLIENT_ID=YOUR_GOOGLE_OAUTH_CLIENT_ID
GOOGLE_OAUTH_CLIENT_SECRET=YOUR_GOOGLE_OAUTH_CLIENT_SECRET
GOOGLE_OAUTH_REDIRECT_URI=https://yourdomain.com/api/auth/google/callback

# 管理员配置
ADMIN_PASSWORD=your-secure-admin-password
SESSION_SECRET=your-secret-key-change-this-in-production

# 应用配置
NEXTAUTH_URL=https://yourdomain.com
```

---

## 🚀 部署步骤

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/ghs-edu-signup.git
cd ghs-edu-signup
npm install
```

### 2. 配置Google Cloud项目

#### 🔧 Google Admin SDK配置（用于创建用户账号）

1. 打开 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 **Admin SDK API**：
   - 导航至 `APIs & Services > Library`
   - 搜索并启用 **Admin SDK API**

4. 创建服务账号凭证：
   - 导航至 `APIs & Services > Credentials`
   - 点击 **Create Credentials > OAuth 2.0 Client ID**
   - 配置 OAuth 同意屏幕（如需要）
   - 添加重定向URI：`https://developers.google.com/oauthplayground`

5. 获取Refresh Token：
   - 访问 [OAuth 2.0 Playground](https://developers.google.com/oauthplayground)
   - 点击齿轮图标，启用 **Use your own OAuth credentials**
   - 输入Client ID和Client Secret
   - 选择作用域：`https://www.googleapis.com/auth/admin.directory.user`
   - 完成授权并获取Refresh Token

#### 🌐 Google OAuth配置（用于学生登录）

1. 在同一个Google Cloud项目中创建另一个OAuth 2.0客户端ID
2. 应用类型选择 **Web application**
3. 添加授权重定向URI：
   ```
   http://localhost:3000/api/auth/google/callback  # 开发环境
   https://yourdomain.com/api/auth/google/callback  # 生产环境
   ```

### 3. 配置环境变量

复制 `.env.local.example` 为 `.env.local` 并填入相应的值：

```bash
cp .env.local.example .env.local
```

### 4. 运行项目

```bash
# 开发环境
npm run dev

# 生产环境
npm run build
npm start
```

---

## 📖 使用说明

### 学生注册流程
1. 访问首页，点击"立即注册"
2. 输入有效的激活码
3. 填写个人信息（用户名、姓名、专业等）
4. 系统自动创建Google Workspace账号

### 学生登录流程
1. 访问首页，点击"登录账号"
2. 使用Google OAuth登录
3. 系统验证邮箱后缀（必须是@ghs.edu.kg）
4. 登录成功后进入学生门户

### 管理员功能
1. 访问 `/admin/login` 进入管理员登录
2. 使用管理员密码登录
3. 在管理面板中：
   - 生成新的激活码
   - 查看激活码使用状态
   - 启用/禁用激活码
   - 删除激活码

---

## 🔒 安全注意事项

1. **更改默认密码**：部署前务必修改 `ADMIN_PASSWORD`
2. **保护敏感信息**：确保 `.env.local` 文件不被提交到版本控制
3. **HTTPS部署**：生产环境必须使用HTTPS
4. **定期更新**：定期更新依赖包和安全补丁

---

## 🛠️ 技术栈

- **前端框架**：Next.js 13
- **样式**：CSS-in-JS (styled-jsx)
- **认证**：Google OAuth 2.0
- **数据存储**：文件系统（JSON）
- **部署平台**：Vercel

---

## 📝 开发团队

Powered by **Garbage Human Studio**

---

## 📄 许可证

© 2025 Great Heights School. All rights reserved.



