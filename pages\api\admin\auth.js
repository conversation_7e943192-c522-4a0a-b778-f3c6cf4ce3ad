// pages/api/admin/auth.js
import cookie from 'cookie'

const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123'
const SESSION_SECRET = process.env.SESSION_SECRET || 'your-secret-key'

export default function handler(req, res) {
  if (req.method === 'POST') {
    const { password } = req.body

    if (!password) {
      return res.status(400).json({ message: '请输入密码' })
    }

    if (password !== ADMIN_PASSWORD) {
      return res.status(401).json({ message: '密码错误' })
    }

    // 创建简单的session token
    const sessionToken = Buffer.from(`admin:${Date.now()}:${SESSION_SECRET}`).toString('base64')

    // 设置session cookie
    res.setHeader('Set-Cookie', cookie.serialize('adminSession', sessionToken, {
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 // 24小时
    }))

    res.status(200).json({ success: true, message: '登录成功' })
  } else if (req.method === 'GET') {
    // 验证session
    const cookies = cookie.parse(req.headers.cookie || '')
    const sessionToken = cookies.adminSession

    if (!sessionToken) {
      return res.status(401).json({ authenticated: false })
    }

    try {
      const decoded = Buffer.from(sessionToken, 'base64').toString()
      const [user, timestamp, secret] = decoded.split(':')
      
      if (user !== 'admin' || secret !== SESSION_SECRET) {
        return res.status(401).json({ authenticated: false })
      }

      // 检查session是否过期（24小时）
      const sessionTime = parseInt(timestamp)
      const now = Date.now()
      const maxAge = 24 * 60 * 60 * 1000 // 24小时

      if (now - sessionTime > maxAge) {
        return res.status(401).json({ authenticated: false, message: 'Session过期' })
      }

      res.status(200).json({ authenticated: true })
    } catch (error) {
      res.status(401).json({ authenticated: false })
    }
  } else if (req.method === 'DELETE') {
    // 登出
    res.setHeader('Set-Cookie', cookie.serialize('adminSession', '', {
      path: '/',
      expires: new Date(0)
    }))

    res.status(200).json({ success: true, message: '已登出' })
  } else {
    res.setHeader('Allow', ['POST', 'GET', 'DELETE'])
    res.status(405).end(`Method ${req.method} Not Allowed`)
  }
}
