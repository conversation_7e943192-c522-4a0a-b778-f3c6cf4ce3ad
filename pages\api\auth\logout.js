// pages/api/auth/logout.js
import cookie from 'cookie'

export default function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  // 清除所有登录相关的cookies
  const clearCookies = [
    cookie.serialize('userEmail', '', { 
      path: '/', 
      expires: new Date(0) 
    }),
    cookie.serialize('userName', '', { 
      path: '/', 
      expires: new Date(0) 
    }),
    cookie.serialize('userPicture', '', { 
      path: '/', 
      expires: new Date(0) 
    }),
    cookie.serialize('googleOAuthState', '', { 
      path: '/', 
      expires: new Date(0) 
    })
  ]

  res.setHeader('Set-Cookie', clearCookies)
  
  // 返回成功响应
  res.status(200).json({ success: true, message: '已成功登出' })
}
