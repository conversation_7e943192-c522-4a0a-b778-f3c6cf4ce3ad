"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/reset-password";
exports.ids = ["pages/reset-password"];
exports.modules = {

/***/ "./pages/reset-password.js":
/*!*********************************!*\
  !*** ./pages/reset-password.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResetPassword),\n/* harmony export */   \"getServerSideProps\": () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n// pages/reset-password.js\n\n\n\n\n\nasync function fetchGoogleToken() {\n    const res = await fetch(\"https://oauth2.googleapis.com/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n            client_id: process.env.GOOGLE_CLIENT_ID,\n            client_secret: process.env.GOOGLE_CLIENT_SECRET,\n            refresh_token: process.env.GOOGLE_REFRESH_TOKEN,\n            grant_type: \"refresh_token\"\n        })\n    });\n    if (!res.ok) return null;\n    const { access_token  } = await res.json();\n    return access_token;\n}\nasync function getServerSideProps({ req  }) {\n    const cookies = (0,cookie__WEBPACK_IMPORTED_MODULE_3__.parse)(req.headers.cookie || \"\");\n    const oauthUsername = cookies.oauthUsername;\n    const trustLevel = parseInt(cookies.oauthTrustLevel || \"0\", 10);\n    if (!oauthUsername || trustLevel < 3) {\n        return {\n            redirect: {\n                destination: \"/\",\n                permanent: false\n            }\n        };\n    }\n    const rawDom = process.env.EMAIL_DOMAIN || \"chatgpt.org.uk\";\n    const domain = rawDom.startsWith(\"@\") ? rawDom : \"@\" + rawDom;\n    const studentEmail = oauthUsername.includes(\"@\") ? oauthUsername : `${oauthUsername}${domain}`;\n    // 确保用户存在\n    const token = await fetchGoogleToken();\n    let exists = false;\n    if (token) {\n        const res = await fetch(`https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(studentEmail)}`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        exists = res.ok;\n    }\n    if (!exists) {\n        return {\n            redirect: {\n                destination: \"/register\",\n                permanent: false\n            }\n        };\n    }\n    return {\n        props: {\n            studentEmail\n        }\n    };\n}\nfunction ResetPassword({ studentEmail  }) {\n    const [pwd, setPwd] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [confirm, setConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!pwd) return alert(\"Please enter a new password.\");\n        if (pwd !== confirm) return alert(\"Passwords do not match.\");\n        const res = await fetch(\"/api/reset-password\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                password: pwd\n            })\n        });\n        if (res.ok) {\n            alert(\"Password reset successful!\");\n            window.location.href = \"/student-portal\";\n        } else {\n            const text = await res.text();\n            alert(\"Reset failed: \" + text);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-9e3787a76295cd7f\",\n                    children: \"Reset Password\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                    lineNumber: 71,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-9e3787a76295cd7f\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"jsx-9e3787a76295cd7f\",\n                        children: \"Reset Your Google Workspace Password\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"jsx-9e3787a76295cd7f\",\n                        children: [\n                            \"Your account: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"jsx-9e3787a76295cd7f\",\n                                children: studentEmail\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                                lineNumber: 74,\n                                columnNumber: 26\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"jsx-9e3787a76295cd7f\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"jsx-9e3787a76295cd7f\",\n                                children: \"New Password:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"password\",\n                                value: pwd,\n                                onChange: (e)=>setPwd(e.target.value),\n                                required: true,\n                                className: \"jsx-9e3787a76295cd7f\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"jsx-9e3787a76295cd7f\",\n                                children: \"Confirm Password:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"password\",\n                                value: confirm,\n                                onChange: (e)=>setConfirm(e.target.value),\n                                required: true,\n                                className: \"jsx-9e3787a76295cd7f\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"jsx-9e3787a76295cd7f\",\n                                children: \"Reset Password\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            textAlign: \"center\",\n                            marginTop: 20\n                        },\n                        className: \"jsx-9e3787a76295cd7f\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/student-portal\",\n                            className: \"jsx-9e3787a76295cd7f\",\n                            children: \"← Back to Portal\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\reset-password.js\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"9e3787a76295cd7f\",\n                children: \".container.jsx-9e3787a76295cd7f{max-width:480px;margin:40px auto;padding:0 20px}h1.jsx-9e3787a76295cd7f{text-align:center;color:#333}form.jsx-9e3787a76295cd7f{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:12px;margin-top:20px}label.jsx-9e3787a76295cd7f{font-weight:bold}input.jsx-9e3787a76295cd7f{padding:10px;border:1px solid#ccc;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}button.jsx-9e3787a76295cd7f{padding:12px;background:#0070f3;color:#fff;border:none;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:16px;cursor:pointer}button.jsx-9e3787a76295cd7f:hover{background:#005bb5}a.jsx-9e3787a76295cd7f{color:#0070f3;text-decoration:none}a.jsx-9e3787a76295cd7f:hover{text-decoration:underline}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/reset-password.js\n");

/***/ }),

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/reset-password.js"));
module.exports = __webpack_exports__;

})();