import Head from 'next/head'
import { useState } from 'react'
import { useRouter } from 'next/router'

export default function AdminLogin() {
  const router = useRouter()
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      })

      const result = await response.json()

      if (response.ok) {
        // 登录成功，跳转到管理面板
        router.push('/admin/dashboard')
      } else {
        setError(result.message || '登录失败')
      }
    } catch (error) {
      setError('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>管理员登录 - Great Heights School</title>
      </Head>
      <div className="container">
        <div className="login-card">
          <div className="header">
            <h1>管理员登录</h1>
            <p>Great Heights School 激活码管理系统</p>
          </div>

          <form onSubmit={handleSubmit}>
            {error && (
              <div className="error-message">
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="password">管理员密码</label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="请输入管理员密码"
                required
                disabled={isLoading}
              />
            </div>

            <button 
              type="submit" 
              className="login-btn"
              disabled={isLoading || !password.trim()}
            >
              {isLoading ? '登录中...' : '登录'}
            </button>
          </form>

          <div className="back-link">
            <a href="/">← 返回首页</a>
          </div>
        </div>
      </div>

      <style jsx>{`
        .container {
          min-height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }
        .login-card {
          background: #fff;
          padding: 40px;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.1);
          width: 100%;
          max-width: 400px;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .header h1 {
          color: #333;
          margin-bottom: 8px;
          font-size: 24px;
          font-weight: 600;
        }
        .header p {
          color: #666;
          font-size: 14px;
          margin: 0;
        }
        .error-message {
          background: #fee;
          color: #c33;
          padding: 12px;
          border-radius: 6px;
          margin-bottom: 20px;
          border: 1px solid #fcc;
          font-size: 14px;
          text-align: center;
        }
        .form-group {
          margin-bottom: 20px;
        }
        label {
          display: block;
          margin-bottom: 6px;
          color: #333;
          font-weight: 500;
          font-size: 14px;
        }
        input {
          width: 100%;
          padding: 12px;
          border: 2px solid #e1e5e9;
          border-radius: 6px;
          font-size: 16px;
          transition: border-color 0.2s ease;
          box-sizing: border-box;
        }
        input:focus {
          outline: none;
          border-color: #4285f4;
        }
        input:disabled {
          background: #f5f5f5;
          cursor: not-allowed;
        }
        .login-btn {
          width: 100%;
          padding: 14px;
          background: #4285f4;
          color: #fff;
          border: none;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background 0.2s ease;
        }
        .login-btn:hover:not(:disabled) {
          background: #3367d6;
        }
        .login-btn:disabled {
          background: #ccc;
          cursor: not-allowed;
        }
        .back-link {
          text-align: center;
          margin-top: 20px;
        }
        .back-link a {
          color: #666;
          text-decoration: none;
          font-size: 14px;
        }
        .back-link a:hover {
          color: #4285f4;
          text-decoration: underline;
        }
      `}</style>
    </>
  )
}
